//
//  IDFANotDeterminedViewController.m
//  GETIDFA
//
//  Created by apple on 2025/6/13.
//

#import "IDFANotDeterminedViewController.h"
#import "IDFADescriptionView.h"

@interface IDFANotDeterminedViewController ()
@property (nonatomic, strong) UIImageView *guideImageView;
@property (nonatomic, strong) UIButton *authorizeButton;
@property (nonatomic, strong) IDFADescriptionView *descriptionView;
@end

@implementation IDFANotDeterminedViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupUI];
}

- (void)setupUI {
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];

    // 标题
    self.titleLabel = [self createTitleLabelWithText:@"获取IDFA权限"];
    self.titleLabel.textColor = theme.primaryTextColor;
    [self.contentView addSubview:self.titleLabel];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.idfaLogoLabel.mas_bottom).offset(20);
        make.left.equalTo(self.contentView).offset(theme.horizontalMargin);
        make.right.equalTo(self.contentView).offset(-theme.horizontalMargin);
    }];

    // IDFA解释说明
    self.descriptionView = [[IDFADescriptionView alloc] init];
    NSArray *descriptions = @[
        @"什么是IDFA？",
        @"IDFA是iOS设备的广告标识符",
        @"可以简单理解为iOS设备的临时身份证",
        @"仅能用于跟踪广告效果",
        @"打通不同App之间的广告"
    ];
    [self.descriptionView setupWithDescriptions:descriptions];
    [self.contentView addSubview:self.descriptionView];
    [self.descriptionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.titleLabel.mas_bottom).offset(theme.smallSpacing);
        make.left.equalTo(self.contentView).offset(theme.horizontalMargin);
        make.right.equalTo(self.contentView).offset(-theme.horizontalMargin);
    }];

    // 授权按钮
    self.authorizeButton = [self createGradientButtonWithTitle:@"去【允许】授权"];
    [self.authorizeButton addTarget:self action:@selector(authorizeButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [self.contentView addSubview:self.authorizeButton];
    [self.authorizeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.descriptionView.mas_bottom).offset(theme.smallSpacing);
        make.left.equalTo(self.view).offset(theme.horizontalMargin+1);
        make.right.equalTo(self.view).offset(-theme.horizontalMargin-1);
        make.height.equalTo(@44);
    }];
    
    // 图片提示文字
    UILabel *bottomTipLabel = [self createInfoLabelWithText:@"系统弹窗中点击允许，才能成功获取 IDFA" fontSize:theme.hintFontSize];
    bottomTipLabel.textColor = [UIColor colorWithRed:0.7 green:0.9 blue:0.7 alpha:1.0];
    bottomTipLabel.font = [UIFont boldSystemFontOfSize:14];
    [self.contentView addSubview:bottomTipLabel];
    [bottomTipLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.authorizeButton.mas_bottom).offset(theme.smallSpacing);
        make.centerX.equalTo(self.contentView);
        make.bottom.equalTo(self.contentView).offset(-10);
    }];

    // 添加按钮点击动画
    [self addButtonAnimation:self.authorizeButton];
}

- (void)addButtonAnimation:(UIButton *)button {
    // 添加按钮点击动画效果
    [button addTarget:self action:@selector(buttonTouchDown:) forControlEvents:UIControlEventTouchDown];
    [button addTarget:self action:@selector(buttonTouchUp:) forControlEvents:UIControlEventTouchUpInside | UIControlEventTouchUpOutside];
}

- (void)buttonTouchDown:(UIButton *)button {
    [UIView animateWithDuration:0.1 animations:^{
        button.transform = CGAffineTransformMakeScale(0.95, 0.95);
    }];
}

- (void)buttonTouchUp:(UIButton *)button {
    [UIView animateWithDuration:0.1 animations:^{
        button.transform = CGAffineTransformIdentity;
    }];
}

- (void)authorizeButtonTapped {
    // 调用系统授权弹窗
    [[TrackingStatusManager sharedManager] requestOrCheckTrackingWithCompletion:^(TrackingStatus newStatus) {
        if (self.onStatusChanged) {
            self.onStatusChanged(newStatus);
        }
    }];
}

@end
