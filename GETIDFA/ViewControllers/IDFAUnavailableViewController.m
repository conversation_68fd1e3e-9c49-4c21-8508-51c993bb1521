//
//  IDFAUnavailableViewController.m
//  GETIDFA
//
//  Created by apple on 2025/6/13.
//

#import "IDFAUnavailableViewController.h"
#import "IDFADescriptionView.h"
#import "IDFALocalizationManager.h"

@interface IDFAUnavailableViewController ()
@property (nonatomic, strong) UILabel *statusLabel;
@property (nonatomic, strong) IDFADescriptionView *descriptionView;
@property (nonatomic, strong) UIImageView *iconImageView;
@end

@implementation IDFAUnavailableViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupUI];
}

- (void)setupUI {
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];

    // 图标
    self.iconImageView = [[UIImageView alloc] init];
    self.iconImageView.image = [UIImage systemImageNamed:@"exclamationmark.triangle"];
    self.iconImageView.tintColor = theme.hintTextColor;
    self.iconImageView.contentMode = UIViewContentModeScaleAspectFit;
    [self.contentView addSubview:self.iconImageView];
    [self.iconImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.idfaLogoLabel.mas_bottom).offset(theme.largeSpacing);
        make.centerX.equalTo(self.contentView);
        make.width.height.equalTo(@80);
    }];

    // 状态标题
    self.statusLabel = [self createTitleLabelWithText:IDFALocalizedString(@"feature_unavailable")];
    self.statusLabel.textColor = theme.hintTextColor;
    [self.contentView addSubview:self.statusLabel];
    [self.statusLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.iconImageView.mas_bottom).offset(theme.mediumSpacing);
        make.left.equalTo(self.contentView).offset(theme.horizontalMargin);
        make.right.equalTo(self.contentView).offset(-theme.horizontalMargin);
    }];

    // 说明文字
    self.descriptionView = [[IDFADescriptionView alloc] init];
    NSArray *descriptions = @[
        @"当前设备或系统版本不支持跟踪功能。",
        @"iOS版本低于14.0",
        @"设备不支持IDFA",
        @"系统功能异常",
        @"升级到iOS 14.0或更高版本",
        @"检查设备兼容性",
        @"重启设备后重试"
    ];
    [self.descriptionView setupWithDescriptions:descriptions];
    [self.contentView addSubview:self.descriptionView];
    [self.descriptionView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.statusLabel.mas_bottom).offset(theme.mediumSpacing);
        make.left.equalTo(self.contentView).offset(theme.horizontalMargin);
        make.right.equalTo(self.contentView).offset(-theme.horizontalMargin);
        make.bottom.equalTo(self.contentView).offset(-30);
    }];
}

@end
