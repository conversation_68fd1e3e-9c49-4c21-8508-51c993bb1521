//
//  IDFABaseViewController.m
//  GETIDFA
//
//  Created by apple on 2025/6/13.
//

#import "IDFABaseViewController.h"
#import "IDFALocalizationManager.h"
#import <objc/runtime.h>

@implementation IDFABaseViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupBaseUI];

    // 监听主题切换通知
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(themeDidChange:)
                                                 name:@"IDFAThemeDidChangeNotification"
                                               object:nil];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    [self.gradientBackgroundView restartAnimations];
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)themeDidChange:(NSNotification *)notification {
    [self updateThemeAppearance];
}

- (UIStackView *)stepsStackView {
    if (!_stepsStackView) {
        _stepsStackView = [[UIStackView alloc] init];
        _stepsStackView.axis = UILayoutConstraintAxisVertical;
        _stepsStackView.spacing = 12;
        _stepsStackView.alignment = UIStackViewAlignmentFill;
        [self.contentView addSubview:self.stepsStackView];
    }
    return _stepsStackView;
}

- (void)setupBaseUI {
    // 设置渐变背景
    self.gradientBackgroundView = [[IDFAGradientView alloc] init];
    [self.view addSubview:self.gradientBackgroundView];
    [self.gradientBackgroundView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view);
    }];
    
    // 设置滚动视图
    self.scrollView = [[UIScrollView alloc] init];
    self.scrollView.showsVerticalScrollIndicator = NO;
    self.scrollView.showsHorizontalScrollIndicator = NO;
    [self.view addSubview:self.scrollView];
    [self.scrollView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view.mas_safeAreaLayoutGuide);
    }];
    
    // 内容视图
    self.contentView = [[UIView alloc] init];
    [self.scrollView addSubview:self.contentView];
    [self.contentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.scrollView);
        make.width.equalTo(self.scrollView);
    }];
    
    [self setupIDFALogo];
    
    // 语言按钮
    self.languageButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.languageButton setImage:[UIImage imageNamed:@"language"] forState:(UIControlStateNormal)];
    [self.languageButton addTarget:self action:@selector(languageButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [self.contentView addSubview:self.languageButton];
    
    // 主题按钮
    self.themeButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [self.themeButton addTarget:self action:@selector(themeButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [self.contentView addSubview:self.themeButton];
    
    [self.languageButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.contentView).offset(15);
        make.top.equalTo(self.contentView).offset(15);
        make.width.equalTo(@30);
        make.height.equalTo(@30);
    }];
    
    [self.themeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.contentView).offset(-15);
        make.top.equalTo(self.contentView).offset(15);
        make.width.equalTo(@30);
        make.height.equalTo(@30);
    }];
    
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];
    
    if (theme.currentTheme == IDFAThemeTypeDark) {
//        [self.themeButton setTitle:IDFALocalizedString(@"light_theme") forState:UIControlStateNormal];
        [self.themeButton setImage:[UIImage imageNamed:@"theme_light"] forState:(UIControlStateNormal)];
    } else {
//        [self.themeButton setTitle:IDFALocalizedString(@"dark_theme") forState:UIControlStateNormal];
        [self.themeButton setImage:[UIImage imageNamed:@"theme_dark"] forState:(UIControlStateNormal)];
    }
}

- (void)languageButtonTapped {
    if ([self.delegate respondsToSelector:@selector(topNavigationBarDidTapLanguage)]) {
        [self.delegate topNavigationBarDidTapLanguage];
    }
}

- (void)themeButtonTapped {
    if ([self.delegate respondsToSelector:@selector(topNavigationBarDidTapTheme)]) {
        [self.delegate topNavigationBarDidTapTheme];
    }
}

- (void)setupIDFALogo {
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];

    // 创建3D立体IDFA标题容器
    UIView *logoContainer = [[UIView alloc] init];
    [self.contentView addSubview:logoContainer];
    [logoContainer mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentView).offset(theme.smallSpacing-15);
        make.centerX.equalTo(self.contentView);
        make.width.equalTo(@200);
        make.height.equalTo(@60);
    }];

    // 创建真正的3D立体效果
    [self create3DTextInContainer:logoContainer withText:@"GETIDFA" fontSize:theme.logoFontSize];
}

- (IDFAGradientButton *)createGradientButtonWithTitle:(NSString *)title {
    return [[IDFAGradientButton alloc] initWithTitle:title];
}

- (IDFAGradientButton *)createGradientButtonWithTitle:(NSString *)title size:(IDFAButtonSize)size {
    return [[IDFAGradientButton alloc] initWithTitle:title size:size];
}

- (UILabel *)createInfoLabelWithText:(NSString *)text fontSize:(CGFloat)fontSize {
    UILabel *label = [[UILabel alloc] init];
    label.text = text;
    label.font = [UIFont systemFontOfSize:fontSize];
    label.textColor = [UIColor whiteColor];
    label.textAlignment = NSTextAlignmentCenter;
    label.numberOfLines = 0;

    // 添加文字阴影
    label.layer.shadowColor = [UIColor blackColor].CGColor;
    label.layer.shadowOffset = CGSizeMake(1, 1);
    label.layer.shadowRadius = 2;
    label.layer.shadowOpacity = 0.5;

    return label;
}

- (UILabel *)createTitleLabelWithText:(NSString *)text {
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];

    UILabel *label = [[UILabel alloc] init];
    label.text = text;
    label.font = [UIFont boldSystemFontOfSize:theme.titleFontSize];
    label.textAlignment = NSTextAlignmentCenter;
    label.numberOfLines = 0;

    // 阴影效果
    label.layer.shadowColor = [UIColor blackColor].CGColor;
    label.layer.shadowOffset = theme.shadowOffset;
    label.layer.shadowRadius = theme.shadowRadius;
    label.layer.shadowOpacity = theme.shadowOpacity;

    return label;
}

- (UILabel *)createDescriptionLabelWithText:(NSString *)text {
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];

    UILabel *label = [[UILabel alloc] init];
    label.text = text;
    label.font = [UIFont systemFontOfSize:theme.descriptionFontSize];
    label.textColor = theme.secondaryTextColor;
    label.textAlignment = NSTextAlignmentCenter;
    label.numberOfLines = 0;

    // 设置行间距
    NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
    paragraphStyle.lineSpacing = 4;
    paragraphStyle.alignment = NSTextAlignmentCenter;

    NSAttributedString *attributedString = [[NSAttributedString alloc]
        initWithString:text
        attributes:@{NSParagraphStyleAttributeName: paragraphStyle}];
    label.attributedText = attributedString;

    // 文字阴影
    label.layer.shadowColor = [UIColor blackColor].CGColor;
    label.layer.shadowOffset = CGSizeMake(1, 1);
    label.layer.shadowRadius = 2;
    label.layer.shadowOpacity = 0.3;

    return label;
}

- (UIImageView *)createTappableImageViewWithImageName:(NSString *)imageName {
    UIImageView *imageView = [[UIImageView alloc] init];
    imageView.image = [UIImage imageNamed:imageName];
    imageView.contentMode = UIViewContentModeScaleAspectFit;
    imageView.userInteractionEnabled = YES;

    // 设置样式
    imageView.layer.cornerRadius = 12;
    imageView.layer.masksToBounds = YES;
    imageView.layer.borderWidth = 2;
    imageView.layer.borderColor = [UIColor colorWithWhite:1.0 alpha:0.4].CGColor;

    // 添加阴影
    imageView.layer.shadowColor = [UIColor blackColor].CGColor;
    imageView.layer.shadowOffset = CGSizeMake(0, 4);
    imageView.layer.shadowRadius = 8;
    imageView.layer.shadowOpacity = 0.3;
    imageView.layer.masksToBounds = NO;

    // 添加点击手势
    UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc]
        initWithTarget:self action:@selector(imageViewTapped:)];
    [imageView addGestureRecognizer:tapGesture];

    // 添加放大提示图标
    UIImageView *zoomIcon = [[UIImageView alloc] init];
    zoomIcon.image = [UIImage systemImageNamed:@"magnifyingglass"];
    zoomIcon.tintColor = [UIColor colorWithWhite:1.0 alpha:0.8];
    zoomIcon.layer.cornerRadius = 9;
    zoomIcon.layer.masksToBounds = YES;
    [imageView addSubview:zoomIcon];
    [zoomIcon mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(imageView).offset(-2);
        make.top.equalTo(imageView).offset(2);
        make.width.height.equalTo(@18);
    }];

    return imageView;
}

- (void)imageViewTapped:(UITapGestureRecognizer *)gesture {
    UIImageView *imageView = (UIImageView *)gesture.view;
    [self showImageZoomView:imageView.image];
}

- (void)showImageZoomView:(UIImage *)image {
    // 创建全屏背景
    UIView *backgroundView = [[UIView alloc] init];
    backgroundView.backgroundColor = [UIColor colorWithWhite:0 alpha:0.9];
    backgroundView.alpha = 0;
    [self.view addSubview:backgroundView];
    [backgroundView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view);
    }];

    // 创建放大的图片视图
    UIImageView *zoomedImageView = [[UIImageView alloc] init];
    zoomedImageView.image = image;
    zoomedImageView.contentMode = UIViewContentModeScaleAspectFit;
    zoomedImageView.layer.cornerRadius = 16;
    zoomedImageView.layer.masksToBounds = YES;
    zoomedImageView.transform = CGAffineTransformMakeScale(0.1, 0.1);
    [backgroundView addSubview:zoomedImageView];
    [zoomedImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(backgroundView);
        make.left.equalTo(backgroundView).offset(40);
        make.right.equalTo(backgroundView).offset(-40);
        make.height.lessThanOrEqualTo(backgroundView).multipliedBy(0.7);
    }];

    // 添加关闭按钮
    UIButton *closeButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [closeButton setImage:[UIImage systemImageNamed:@"xmark.circle.fill"] forState:UIControlStateNormal];
    closeButton.tintColor = [UIColor whiteColor];
    closeButton.backgroundColor = [UIColor colorWithWhite:0 alpha:0.6];
    closeButton.layer.cornerRadius = 20;
    [closeButton addTarget:self action:@selector(closeImageZoomView:) forControlEvents:UIControlEventTouchUpInside];
    [backgroundView addSubview:closeButton];
    [closeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(backgroundView.mas_safeAreaLayoutGuideTop).offset(20);
        make.right.equalTo(backgroundView).offset(-20);
        make.width.height.equalTo(@40);
    }];

    // 添加点击背景关闭手势
    UITapGestureRecognizer *tapGesture = [[UITapGestureRecognizer alloc]
        initWithTarget:self action:@selector(closeImageZoomView:)];
    [backgroundView addGestureRecognizer:tapGesture];

    // 动画显示
    [UIView animateWithDuration:0.3 delay:0 usingSpringWithDamping:0.8 initialSpringVelocity:0 options:0 animations:^{
        backgroundView.alpha = 1;
        zoomedImageView.transform = CGAffineTransformIdentity;
    } completion:nil];
}

- (void)closeImageZoomView:(id)sender {
    UIView *backgroundView = nil;
    if ([sender isKindOfClass:[UIButton class]]) {
        backgroundView = ((UIButton *)sender).superview;
    } else if ([sender isKindOfClass:[UITapGestureRecognizer class]]) {
        backgroundView = ((UITapGestureRecognizer *)sender).view;
    }

    if (backgroundView) {
        [UIView animateWithDuration:0.2 animations:^{
            backgroundView.alpha = 0;
            for (UIView *subview in backgroundView.subviews) {
                if ([subview isKindOfClass:[UIImageView class]]) {
                    subview.transform = CGAffineTransformMakeScale(0.1, 0.1);
                }
            }
        } completion:^(BOOL finished) {
            [backgroundView removeFromSuperview];
        }];
    }
}

- (void)showCopySuccessMessage {
    UILabel *messageLabel = [[UILabel alloc] init];
    messageLabel.text = IDFALocalizedString(@"copied_success");
    messageLabel.font = [UIFont boldSystemFontOfSize:16];
    messageLabel.textColor = [UIColor whiteColor];
    messageLabel.textAlignment = NSTextAlignmentCenter;
    messageLabel.backgroundColor = [UIColor colorWithWhite:0 alpha:0.8];
    messageLabel.layer.cornerRadius = 20;
    messageLabel.layer.masksToBounds = YES;
    messageLabel.alpha = 0;

    [self.view addSubview:messageLabel];
    [messageLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.center.equalTo(self.view);
        make.width.equalTo(@160);
        make.height.equalTo(@40);
    }];

    [UIView animateWithDuration:0.3 animations:^{
        messageLabel.alpha = 1;
    } completion:^(BOOL finished) {
        [UIView animateWithDuration:0.3 delay:1.5 options:0 animations:^{
            messageLabel.alpha = 0;
        } completion:^(BOOL finished) {
            [messageLabel removeFromSuperview];
        }];
    }];
}

- (void)updateThemeAppearance {
    // 子类重写此方法来更新主题外观
    // 更新渐变背景
    [self.gradientBackgroundView updateThemeAppearance];
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];
    
    if (theme.currentTheme == IDFAThemeTypeDark) {
//        [self.themeButton setTitle:IDFALocalizedString(@"light_theme") forState:UIControlStateNormal];
        [self.themeButton setImage:[UIImage imageNamed:@"theme_light"] forState:(UIControlStateNormal)];
    } else {
//        [self.themeButton setTitle:IDFALocalizedString(@"dark_theme") forState:UIControlStateNormal];
        [self.themeButton setImage:[UIImage imageNamed:@"theme_dark"] forState:(UIControlStateNormal)];
    }
}

- (void)create3DTextInContainer:(UIView *)container withText:(NSString *)text fontSize:(CGFloat)fontSize {
    // 清除容器中的所有子视图
    for (UIView *subview in container.subviews) {
        [subview removeFromSuperview];
    }

    // 创建多层文字实现3D效果
    NSInteger layerCount = 8; // 3D层数

    for (NSInteger i = layerCount - 1; i >= 0; i--) {
        UILabel *layer = [[UILabel alloc] init];
        layer.text = text;
        layer.font = [UIFont boldSystemFontOfSize:fontSize];
        layer.textAlignment = NSTextAlignmentCenter;
        layer.numberOfLines = 1;

        // 根据层级设置颜色和位置
        if (i == 0) {
            // 最前层 - 主要文字，使用渐变色效果
            [self applyGradientToLabel:layer];

            // 添加强烈的发光效果
            layer.layer.shadowColor = [UIColor colorWithRed:0.3 green:0.6 blue:1.0 alpha:1.0].CGColor;
            layer.layer.shadowOffset = CGSizeMake(0, 0);
            layer.layer.shadowRadius = 8;
            layer.layer.shadowOpacity = 0.9;
        } else if (i == 1) {
            // 第二层 - 高亮边缘
            layer.textColor = [UIColor colorWithRed:0.8 green:0.9 blue:1.0 alpha:0.8];
            layer.layer.shadowColor = [UIColor colorWithRed:0.4 green:0.7 blue:1.0 alpha:1.0].CGColor;
            layer.layer.shadowOffset = CGSizeMake(0, 0);
            layer.layer.shadowRadius = 4;
            layer.layer.shadowOpacity = 0.6;
        } else {
            // 后面的层 - 3D深度效果
            CGFloat alpha = 0.5 - (i * 0.05); // 逐渐变暗
            CGFloat blue = 0.9 - (i * 0.08);  // 蓝色逐渐减少
            layer.textColor = [UIColor colorWithRed:0.2 green:0.4 blue:blue alpha:alpha];
        }

        [container addSubview:layer];

        // 设置约束，每层稍微偏移创造3D效果
        CGFloat offsetX = i * 1.5; // X轴偏移
        CGFloat offsetY = i * 1.0; // Y轴偏移

        [layer mas_makeConstraints:^(MASConstraintMaker *make) {
            make.centerX.equalTo(container).offset(offsetX);
            make.centerY.equalTo(container).offset(offsetY);
            make.width.lessThanOrEqualTo(container);
            make.height.lessThanOrEqualTo(container);
        }];
    }

    // 添加整体发光效果
    container.layer.shadowColor = [UIColor colorWithRed:0.3 green:0.6 blue:1.0 alpha:1.0].CGColor;
    container.layer.shadowOffset = CGSizeMake(0, 0);
    container.layer.shadowRadius = 15;
    container.layer.shadowOpacity = 0.6;

    // 存储主标签引用（最前层）
    if (container.subviews.count > 0) {
        self.idfaLogoLabel = (UILabel *)container.subviews.lastObject;
    }
}

- (void)applyGradientToLabel:(UILabel *)label {
    // 创建渐变色文字效果
    NSString *text = label.text;
    NSMutableAttributedString *attributedString = [[NSMutableAttributedString alloc] initWithString:text];

    // 设置基础属性
    [attributedString addAttribute:NSFontAttributeName
                             value:label.font
                             range:NSMakeRange(0, text.length)];

    // 为每个字符设置不同的颜色，创造渐变效果
    for (NSInteger i = 0; i < text.length; i++) {
        CGFloat ratio = (CGFloat)i / (CGFloat)(text.length - 1);

        // 从亮蓝色渐变到白色
        CGFloat red = 0.7 + (0.3 * ratio);    // 0.7 -> 1.0
        CGFloat green = 0.8 + (0.2 * ratio);  // 0.8 -> 1.0
        CGFloat blue = 1.0;                   // 保持1.0

        UIColor *color = [UIColor colorWithRed:red green:green blue:blue alpha:1.0];
        [attributedString addAttribute:NSForegroundColorAttributeName
                                 value:color
                                 range:NSMakeRange(i, 1)];
    }

    label.attributedText = attributedString;
}

- (void)addStepWithNumber:(NSInteger)stepNumber title:(NSString *)title imageName:(NSString *)imageName {
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];

    // 步骤容器
    UIView *stepContainer = [[UIView alloc] init];
    stepContainer.backgroundColor = theme.stepContainerBackgroundColor;
    stepContainer.layer.cornerRadius = 15;
    stepContainer.layer.borderWidth = theme.borderWidth;
    stepContainer.layer.borderColor = theme.borderColor.CGColor;

    // 添加阴影
    stepContainer.layer.shadowColor = [UIColor blackColor].CGColor;
    stepContainer.layer.shadowOffset = CGSizeMake(0, 2);
    stepContainer.layer.shadowRadius = 4;
    stepContainer.layer.shadowOpacity = 0.25;
    stepContainer.layer.masksToBounds = NO;

    [self.stepsStackView addArrangedSubview:stepContainer];
    [stepContainer mas_makeConstraints:^(MASConstraintMaker *make) {
        make.height.equalTo(@90);
    }];

    // 步骤编号
    UILabel *stepNumberLabel = [[UILabel alloc] init];
    stepNumberLabel.text = [NSString stringWithFormat:@"%ld", (long)stepNumber];
    stepNumberLabel.font = [UIFont boldSystemFontOfSize:18];
    stepNumberLabel.textColor = theme.primaryTextColor;
    stepNumberLabel.textAlignment = NSTextAlignmentCenter;
    NSArray *buttonColors = theme.buttonGradientColors;
    stepNumberLabel.backgroundColor = buttonColors.firstObject;
    stepNumberLabel.layer.cornerRadius = 15;
    stepNumberLabel.layer.masksToBounds = YES;
    [stepContainer addSubview:stepNumberLabel];
    [stepNumberLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(stepContainer).offset(theme.smallSpacing);
        make.centerY.equalTo(stepContainer);
        make.width.height.equalTo(@30);
    }];

    // 步骤标题
    UILabel *stepTitleLabel = [self createInfoLabelWithText:title fontSize:15];
    stepTitleLabel.textAlignment = NSTextAlignmentLeft;
    stepTitleLabel.font = [UIFont boldSystemFontOfSize:15];
    [stepContainer addSubview:stepTitleLabel];
    [stepTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(stepNumberLabel.mas_right).offset(theme.smallSpacing);
        make.centerY.equalTo(stepContainer);
        make.right.equalTo(stepContainer).offset(-90);
    }];

    // 引导图片（可点击放大）
    UIImageView *guideImageView = [self createTappableImageViewWithImageName:imageName];
    [stepContainer addSubview:guideImageView];
    [guideImageView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(stepContainer).offset(-theme.smallSpacing);
        make.centerY.equalTo(stepContainer);
        make.width.equalTo(@70);
        make.height.equalTo(@70);
    }];
}
@end
