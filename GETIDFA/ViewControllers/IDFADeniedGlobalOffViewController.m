//
//  IDFADeniedGlobalOffViewController.m
//  GETIDFA
//
//  Created by apple on 2025/6/13.
//

#import "IDFADeniedGlobalOffViewController.h"

@interface IDFADeniedGlobalOffViewController ()
@property (nonatomic, strong) UILabel *statusLabel;
@property (nonatomic, strong) UILabel *instructionLabel;
@end

@implementation IDFADeniedGlobalOffViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupUI];
}

- (void)setupUI {
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];

    // 状态标题
    self.statusLabel = [self createTitleLabelWithText:@"系统跟踪权限已关闭"];
    self.statusLabel.textColor = theme.warningTextColor;
    [self.contentView addSubview:self.statusLabel];
    [self.statusLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.idfaLogoLabel.mas_bottom).offset(20);
        make.left.equalTo(self.contentView).offset(theme.horizontalMargin);
        make.right.equalTo(self.contentView).offset(-theme.horizontalMargin);
    }];

    // 说明文字
    NSString *instructionText = @"系统\"隐私 > 跟踪\"全局开关已关闭，无法弹出请求。\n请按照以下步骤开启系统跟踪权限:";
    self.instructionLabel = [self createDescriptionLabelWithText:instructionText];
    [self.contentView addSubview:self.instructionLabel];
    [self.instructionLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.statusLabel.mas_bottom).offset(15);
        make.left.equalTo(self.contentView).offset(theme.horizontalMargin);
        make.right.equalTo(self.contentView).offset(-theme.horizontalMargin);
    }];
    
    // 步骤引导
    [self.stepsStackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.instructionLabel.mas_bottom).offset(20);
        make.left.equalTo(self.contentView).offset(theme.smallSpacing);
        make.right.equalTo(self.contentView).offset(-theme.smallSpacing);
        make.bottom.equalTo(self.contentView).offset(-10);
    }];

    // 添加步骤
    [self addStepWithNumber:1 title:@"打开系统设置" imageName:@"guide_global01"];
    [self addStepWithNumber:2 title:@"进入隐私安全 > 跟踪" imageName:@"guide_global02"];
    [self addStepWithNumber:3 title:@"开启\"允许App请求跟踪\"" imageName:@"guide_global03"];
}

@end
