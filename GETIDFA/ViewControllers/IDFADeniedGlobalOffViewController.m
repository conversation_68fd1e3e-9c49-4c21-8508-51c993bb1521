//
//  IDFADeniedGlobalOffViewController.m
//  GETIDFA
//
//  Created by apple on 2025/6/13.
//

#import "IDFADeniedGlobalOffViewController.h"
#import "IDFALocalizationManager.h"

@interface IDFADeniedGlobalOffViewController ()
@property (nonatomic, strong) UILabel *statusLabel;
@property (nonatomic, strong) UILabel *instructionLabel;
@end

@implementation IDFADeniedGlobalOffViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupUI];
}

- (void)setupUI {
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];

    // 状态标题
    self.statusLabel = [self createTitleLabelWithText:IDFALocalizedString(@"system_tracking_disabled")];
    self.statusLabel.textColor = theme.warningTextColor;
    [self.contentView addSubview:self.statusLabel];
    [self.statusLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.idfaLogoLabel.mas_bottom).offset(20);
        make.left.equalTo(self.contentView).offset(theme.horizontalMargin);
        make.right.equalTo(self.contentView).offset(-theme.horizontalMargin);
    }];

    // 说明文字
    NSString *instructionText = IDFALocalizedString(@"system_instruction_text");
    self.instructionLabel = [self createDescriptionLabelWithText:instructionText];
    [self.contentView addSubview:self.instructionLabel];
    [self.instructionLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.statusLabel.mas_bottom).offset(15);
        make.left.equalTo(self.contentView).offset(theme.horizontalMargin);
        make.right.equalTo(self.contentView).offset(-theme.horizontalMargin);
    }];
    
    // 步骤引导
    [self.stepsStackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.instructionLabel.mas_bottom).offset(20);
        make.left.equalTo(self.contentView).offset(theme.smallSpacing);
        make.right.equalTo(self.contentView).offset(-theme.smallSpacing);
        make.bottom.equalTo(self.contentView).offset(-10);
    }];

    // 添加步骤
    [self addStepWithNumber:1 title:IDFALocalizedString(@"open_system_settings") imageName:@"guide_global01"];
    [self addStepWithNumber:2 title:IDFALocalizedString(@"privacy_tracking_settings") imageName:@"guide_global02"];
    [self addStepWithNumber:3 title:IDFALocalizedString(@"enable_app_tracking") imageName:@"guide_global03"];
}

@end
