//
//  IDFABaseViewController.h
//  GETIDFA
//
//  Created by apple on 2025/6/13.
//

#import <UIKit/UIKit.h>
#import "IDFAGradientView.h"
#import "IDFAThemeManager.h"
#import "IDFAGradientButton.h"
#import "Masonry.h"

NS_ASSUME_NONNULL_BEGIN

@interface IDFABaseViewController : UIViewController

@property(nonatomic, strong) IDFAGradientView *gradientBackgroundView;
@property(nonatomic, strong) UILabel *idfaLogoLabel;
@property(nonatomic, strong) UILabel *titleLabel;
@property(nonatomic, strong) UILabel *descriptionLabel;
@property(nonatomic, strong) UIScrollView *scrollView;
@property(nonatomic, strong) UIView *contentView;

@property (nonatomic, strong) UIStackView *stepsStackView;

// 设置基础UI
- (void)setupBaseUI;

// 创建3D IDFA标题
- (void)setupIDFALogo;

// 创建渐变按钮
- (IDFAGradientButton *)createGradientButtonWithTitle:(NSString *)title;
- (IDFAGradientButton *)createGradientButtonWithTitle:(NSString *)title size:(IDFAButtonSize)size;

// 创建信息标签
- (UILabel *)createInfoLabelWithText:(NSString *)text fontSize:(CGFloat)fontSize;

// 创建标题标签（更大字体，更好排版）
- (UILabel *)createTitleLabelWithText:(NSString *)text;

// 创建描述标签（适中字体，行间距优化）
- (UILabel *)createDescriptionLabelWithText:(NSString *)text;

// 创建可点击放大的图片视图
- (UIImageView *)createTappableImageViewWithImageName:(NSString *)imageName;

// 显示图片放大视图
- (void)showImageZoomView:(UIImage *)image;

// 显示复制成功提示
- (void)showCopySuccessMessage;

// 主题更新
- (void)updateThemeAppearance;

// 3D文字效果
- (void)create3DTextInContainer:(UIView *)container withText:(NSString *)text fontSize:(CGFloat)fontSize;

- (void)addStepWithNumber:(NSInteger)stepNumber title:(NSString *)title imageName:(NSString *)imageName;
@end

NS_ASSUME_NONNULL_END
