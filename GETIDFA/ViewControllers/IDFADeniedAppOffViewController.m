//
//  IDFADeniedAppOffViewController.m
//  GETIDFA
//
//  Created by apple on 2025/6/13.
//

#import "IDFADeniedAppOffViewController.h"
#import "IDFALocalizationManager.h"

@interface IDFADeniedAppOffViewController ()
@property (nonatomic, strong) UILabel *statusLabel;
@property (nonatomic, strong) UILabel *instructionLabel;
@property (nonatomic, strong) UIButton *openSettingsButton;
@property (nonatomic, strong) UILabel *manualStepsLabel;
@end

@implementation IDFADeniedAppOffViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupUI];
}

- (void)setupUI {
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];

    // 状态标题
    self.statusLabel = [self createTitleLabelWithText:IDFALocalizedString(@"app_tracking_denied")];
    self.statusLabel.textColor = theme.errorTextColor;
    [self.contentView addSubview:self.statusLabel];
    [self.statusLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.idfaLogoLabel.mas_bottom).offset(20);
        make.left.equalTo(self.contentView).offset(theme.horizontalMargin);
        make.right.equalTo(self.contentView).offset(-theme.horizontalMargin);
    }];

    // 说明文字
    NSString *instructionText = IDFALocalizedString(@"app_instruction_text");
    self.instructionLabel = [self createDescriptionLabelWithText:instructionText];
    [self.contentView addSubview:self.instructionLabel];
    [self.instructionLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.statusLabel.mas_bottom).offset(15);
        make.left.equalTo(self.contentView).offset(theme.horizontalMargin);
        make.right.equalTo(self.contentView).offset(-theme.horizontalMargin);
    }];

    // 前往设置按钮
    self.openSettingsButton = [self createGradientButtonWithTitle:IDFALocalizedString(@"go_to_settings")];
    [self.openSettingsButton addTarget:self action:@selector(openSettingsButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [self.contentView addSubview:self.openSettingsButton];
    [self.openSettingsButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.instructionLabel.mas_bottom).offset(10);
        make.left.equalTo(self.view).offset(theme.smallSpacing);
        make.right.equalTo(self.view).offset(-theme.smallSpacing);
        make.height.equalTo(@44);
    }];
    
    // 手动操作步骤标题
    UILabel *manualTitleLabel = [self createDescriptionLabelWithText:IDFALocalizedString(@"manual_steps_text")];
    [self.contentView addSubview:manualTitleLabel];
    [manualTitleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.openSettingsButton.mas_bottom).offset(10);
        make.left.equalTo(self.contentView).offset(25);
        make.right.equalTo(self.contentView).offset(-25);
    }];

    // 步骤引导容器
    [self.contentView addSubview:self.stepsStackView];
    [self.stepsStackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(manualTitleLabel.mas_bottom).offset(10);
        make.left.equalTo(self.contentView).offset(theme.smallSpacing);
        make.right.equalTo(self.contentView).offset(-theme.smallSpacing);
    }];

    // 添加步骤
    [self addStepWithNumber:1 title:IDFALocalizedString(@"open_system_settings") imageName:@"guide_app01"];
    [self addStepWithNumber:2 title:IDFALocalizedString(@"find_idfa_app") imageName:@"guide_app02"];
    [self addStepWithNumber:3 title:IDFALocalizedString(@"enable_tracking") imageName:@"guide_app03"];

    // 底部提示
    UILabel *bottomTipLabel = [self createDescriptionLabelWithText:IDFALocalizedString(@"return_after_enable")];
    bottomTipLabel.textColor = [UIColor colorWithRed:0.7 green:0.9 blue:0.7 alpha:1.0];
    bottomTipLabel.font = [UIFont boldSystemFontOfSize:16];
    [self.contentView addSubview:bottomTipLabel];
    [bottomTipLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.stepsStackView.mas_bottom).offset(10);
        make.left.equalTo(self.contentView).offset(25);
        make.right.equalTo(self.contentView).offset(-25);
        make.bottom.equalTo(self.contentView).offset(-10);
    }];
    
    // 添加按钮动画
    [self addButtonAnimation:self.openSettingsButton];
}

- (void)addButtonAnimation:(UIButton *)button {
    [button addTarget:self action:@selector(buttonTouchDown:) forControlEvents:UIControlEventTouchDown];
    [button addTarget:self action:@selector(buttonTouchUp:) forControlEvents:UIControlEventTouchUpInside | UIControlEventTouchUpOutside];
}

- (void)buttonTouchDown:(UIButton *)button {
    [UIView animateWithDuration:0.1 animations:^{
        button.transform = CGAffineTransformMakeScale(0.95, 0.95);
    }];
}

- (void)buttonTouchUp:(UIButton *)button {
    [UIView animateWithDuration:0.1 animations:^{
        button.transform = CGAffineTransformIdentity;
    }];
}

- (void)openSettingsButtonTapped {
    NSURL *url = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
    if ([[UIApplication sharedApplication] canOpenURL:url]) {
        [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:nil];
    }
}

@end
