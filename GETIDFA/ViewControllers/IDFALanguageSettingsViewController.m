//
//  IDFALanguageSettingsViewController.m
//  GETIDFA
//
//  Created by apple on 2025/6/16.
//

#import "IDFALanguageSettingsViewController.h"
#import "IDFALocalizationManager.h"
#import "IDFAThemeManager.h"
#import "IDFAGradientView.h"
#import "IDFAGradientButton.h"
#import "Masonry.h"

@interface IDFALanguageSettingsViewController () <UITableViewDelegate, UITableViewDataSource>
@property (nonatomic, strong) IDFAGradientView *backgroundView;
@property (nonatomic, strong) UIView *containerView;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UITableView *tableView;
@property (nonatomic, strong) IDFAGradientButton *closeButton;
@property (nonatomic, strong) NSArray<NSDictionary *> *languageOptions;
@end

@implementation IDFALanguageSettingsViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    [self setupLanguageOptions];
    [self setupUI];
    [self setupConstraints];
    [self.backgroundView restartAnimations];
    
    // 监听主题变更
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(themeDidChange:)
                                                 name:@"IDFAThemeDidChangeNotification"
                                               object:nil];
    
    // 监听语言变更
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(languageDidChange:)
                                                 name:@"IDFALanguageDidChangeNotification"
                                               object:nil];
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)setupLanguageOptions {
    self.languageOptions = @[
        @{@"title": @"简体中文", @"type": @(IDFALanguageTypeSimplifiedChinese)},
        @{@"title": @"繁體中文", @"type": @(IDFALanguageTypeTraditionalChinese)},
        @{@"title": @"English", @"type": @(IDFALanguageTypeEnglish)},
        @{@"title": @"日本語", @"type": @(IDFALanguageTypeJapanese)},
        @{@"title": @"한국어", @"type": @(IDFALanguageTypeKorean)},
        @{@"title": @"Русский", @"type": @(IDFALanguageTypeRussian)},
        @{@"title": @"ไทย", @"type": @(IDFALanguageTypeThai)},
        @{@"title": @"Tiếng Việt", @"type": @(IDFALanguageTypeVietnamese)}
    ];
}

- (void)setupUI {
    
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];
    
    // 背景渐变
    self.backgroundView = [[IDFAGradientView alloc] init];
    [self.view addSubview:self.backgroundView];
    
    // 容器视图
    self.containerView = [[UIView alloc] init];
    self.containerView.backgroundColor = [UIColor clearColor];
    [self.view addSubview:self.containerView];
    
    // 标题
    self.titleLabel = [[UILabel alloc] init];
    self.titleLabel.text = @"语言设置";
    self.titleLabel.font = [UIFont boldSystemFontOfSize:24];
    self.titleLabel.textAlignment = NSTextAlignmentCenter;
    [self.containerView addSubview:self.titleLabel];
    
    // 表格视图
    self.tableView = [[UITableView alloc] initWithFrame:CGRectZero style:UITableViewStylePlain];
    self.tableView.delegate = self;
    self.tableView.dataSource = self;
    self.tableView.backgroundColor = [UIColor clearColor];
    self.tableView.separatorStyle = UITableViewCellSeparatorStyleNone;
    self.tableView.showsVerticalScrollIndicator = NO;
    self.tableView.rowHeight = 60;
    [self.tableView registerClass:[UITableViewCell class] forCellReuseIdentifier:@"LanguageCell"];
    [self.containerView addSubview:self.tableView];
    
    // 关闭按钮 - 使用小尺寸
    self.closeButton = [[IDFAGradientButton alloc] initWithTitle:@"关闭" size:IDFAButtonSizeSmall];
    [self.closeButton addTarget:self action:@selector(closeButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:self.closeButton];
    [self.closeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.view.mas_safeAreaLayoutGuideBottom).offset(-theme.horizontalMargin);
        make.centerX.equalTo(self.view);
        make.width.equalTo(@160);
        make.height.equalTo(@([self.closeButton recommendedHeight]));
    }];
}

- (void)setupConstraints {
    [self.backgroundView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view);
    }];
    
    [self.containerView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view.mas_safeAreaLayoutGuide);
    }];
    
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.containerView).offset(30);
        make.left.right.equalTo(self.containerView).insets(UIEdgeInsetsMake(0, 20, 0, 20));
    }];
    
    [self.tableView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.titleLabel.mas_bottom).offset(30);
        make.left.right.equalTo(self.containerView).insets(UIEdgeInsetsMake(0, 20, 0, 20));
        make.bottom.equalTo(self.closeButton.mas_top).offset(-30);
    }];
    
    [self.closeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.containerView).offset(-30);
        make.left.right.equalTo(self.containerView).insets(UIEdgeInsetsMake(0, 20, 0, 20));
    }];
}

- (void)updateThemeAppearance {
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];

    // 更新标题颜色
    self.titleLabel.textColor = theme.primaryTextColor;

    // 强制刷新所有cell，确保颜色正确更新
    dispatch_async(dispatch_get_main_queue(), ^{
        [self.tableView reloadData];

        // 额外确保所有可见cell的文本颜色正确
        for (UITableViewCell *cell in self.tableView.visibleCells) {
            cell.textLabel.textColor = theme.primaryTextColor;
        }
    });
}

- (void)themeDidChange:(NSNotification *)notification {
    [self updateThemeAppearance];
}

- (void)languageDidChange:(NSNotification *)notification {
    self.titleLabel.text = IDFALocalizedString(@"language_settings");
    [self.closeButton setTitle:IDFALocalizedString(@"close") forState:UIControlStateNormal];
    [self.tableView reloadData];
}

- (void)closeButtonTapped {
    [self dismissViewControllerAnimated:YES completion:nil];
}

#pragma mark - UITableViewDataSource

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    return self.languageOptions.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"LanguageCell" forIndexPath:indexPath];

    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];

    NSDictionary *option = self.languageOptions[indexPath.row];
    IDFALanguageType languageType = [option[@"type"] integerValue];
    IDFALanguageType currentLanguage = [IDFALocalizationManager sharedManager].currentLanguage;

    // 配置cell外观
    cell.backgroundColor = [UIColor clearColor];
    cell.selectionStyle = UITableViewCellSelectionStyleNone;

    // 重置cell状态 - 确保cell重用时状态正确
    cell.accessoryType = UITableViewCellAccessoryNone;
    cell.tintColor = nil;

    // 移除旧的背景视图
    for (UIView *subview in cell.contentView.subviews) {
        if (subview.tag == 1001) {
            [subview removeFromSuperview];
        }
    }

    // 创建自定义背景视图
    UIView *backgroundView = [[UIView alloc] init];
    backgroundView.tag = 1001;
    backgroundView.layer.cornerRadius = 12;
    backgroundView.layer.masksToBounds = YES;

    [cell.contentView addSubview:backgroundView];
    [backgroundView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(cell.contentView).insets(UIEdgeInsetsMake(5, 0, 5, 0));
    }];

    // 配置文本 - 确保每次都重新设置颜色
    cell.textLabel.text = option[@"title"];
    cell.textLabel.font = [UIFont systemFontOfSize:18];
    // 强制设置文本颜色，确保在任何情况下都是正确的
    cell.textLabel.textColor = theme.primaryTextColor;

    // 清除可能的阴影效果
    cell.textLabel.layer.shadowOpacity = 0;

    // 选中状态
    if (languageType == currentLanguage) {
        cell.accessoryType = UITableViewCellAccessoryCheckmark;
        cell.tintColor = theme.accentColor;
        backgroundView.backgroundColor = [theme.accentColor colorWithAlphaComponent:0.2];
        // 选中状态下确保文本颜色更明显
        cell.textLabel.textColor = theme.primaryTextColor;
    } else {
        cell.accessoryType = UITableViewCellAccessoryNone;
        backgroundView.backgroundColor = [theme.cardBackgroundColor colorWithAlphaComponent:0.8];
        // 非选中状态下也确保文本颜色正确
        cell.textLabel.textColor = theme.primaryTextColor;
    }

    return cell;
}

#pragma mark - UITableViewDelegate

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    NSDictionary *option = self.languageOptions[indexPath.row];
    IDFALanguageType languageType = [option[@"type"] integerValue];

    // 切换语言
    [[IDFALocalizationManager sharedManager] setLanguage:languageType];

    // 刷新表格并确保颜色正确
    [self refreshTableViewWithCorrectColors];
}

- (void)refreshTableViewWithCorrectColors {
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];

    // 刷新表格数据
    [self.tableView reloadData];

    // 确保所有可见cell的文本颜色正确
    dispatch_async(dispatch_get_main_queue(), ^{
        for (UITableViewCell *cell in self.tableView.visibleCells) {
            cell.textLabel.textColor = theme.primaryTextColor;
        }
    });
}

@end
