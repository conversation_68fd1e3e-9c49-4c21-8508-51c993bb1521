//
//  IDFADocumentViewController.m
//  GETIDFA
//
//  Created by apple on 2025/6/13.
//

#import "IDFADocumentViewController.h"
#import "IDFAThemeManager.h"
#import "IDFAGradientView.h"
#import "IDFAGradientButton.h"
#import "IDFALocalizationManager.h"
#import "Masonry.h"

@interface IDFADocumentViewController ()
@property (nonatomic, assign) IDFADocumentType documentType;
@property (nonatomic, strong) IDFAGradientView *gradientBackgroundView;
@property (nonatomic, strong) UIScrollView *scrollView;
@property (nonatomic, strong) UIView *contentView;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UITextView *contentTextView;
@property (nonatomic, strong) IDFAGradientButton *closeButton;
@end

@implementation IDFADocumentViewController

- (instancetype)initWithDocumentType:(IDFADocumentType)type {
    self = [super init];
    if (self) {
        _documentType = type;
    }
    return self;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [self setupUI];
    [self loadContent];
    [self.gradientBackgroundView restartAnimations];
}

- (void)setupUI {
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];
    
    // 渐变背景
    self.gradientBackgroundView = [[IDFAGradientView alloc] init];
    [self.view addSubview:self.gradientBackgroundView];
    [self.gradientBackgroundView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view);
    }];
    
    // 滚动视图
    self.scrollView = [[UIScrollView alloc] init];
    self.scrollView.showsVerticalScrollIndicator = YES;
    self.scrollView.showsHorizontalScrollIndicator = NO;
    [self.view addSubview:self.scrollView];
    [self.scrollView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.view.mas_safeAreaLayoutGuideTop);
        make.left.right.equalTo(self.view);
        make.bottom.equalTo(self.view.mas_safeAreaLayoutGuideBottom).offset(-80);
    }];
    
    // 内容视图
    self.contentView = [[UIView alloc] init];
    [self.scrollView addSubview:self.contentView];
    [self.contentView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.scrollView);
        make.width.equalTo(self.scrollView);
    }];
    
    // 标题
    self.titleLabel = [[UILabel alloc] init];
    self.titleLabel.font = [UIFont boldSystemFontOfSize:theme.titleFontSize + 4];
    self.titleLabel.textColor = theme.primaryTextColor;
    self.titleLabel.textAlignment = NSTextAlignmentCenter;
    self.titleLabel.numberOfLines = 0;
    
    // 添加标题阴影
    self.titleLabel.layer.shadowColor = [UIColor blackColor].CGColor;
    self.titleLabel.layer.shadowOffset = theme.shadowOffset;
    self.titleLabel.layer.shadowRadius = theme.shadowRadius;
    self.titleLabel.layer.shadowOpacity = theme.shadowOpacity;
    
    [self.contentView addSubview:self.titleLabel];
    [self.titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.contentView).offset(theme.smallSpacing);
        make.left.equalTo(self.contentView).offset(theme.horizontalMargin);
        make.right.equalTo(self.contentView).offset(-theme.horizontalMargin);
    }];
    
    // 内容 - 使用UITextView替代UILabel以获得更好的长文本性能
    self.contentTextView = [[UITextView alloc] init];
    self.contentTextView.font = [UIFont systemFontOfSize:theme.descriptionFontSize];
    self.contentTextView.textColor = theme.secondaryTextColor;
    self.contentTextView.backgroundColor = [UIColor clearColor];
    self.contentTextView.editable = NO;
    self.contentTextView.selectable = YES;
    self.contentTextView.scrollEnabled = NO; // 由外层ScrollView处理滚动
    self.contentTextView.textContainerInset = UIEdgeInsetsZero;
    self.contentTextView.textContainer.lineFragmentPadding = 0;
    self.contentTextView.showsVerticalScrollIndicator = NO;
    self.contentTextView.showsHorizontalScrollIndicator = NO;
    
    [self.contentView addSubview:self.contentTextView];
    [self.contentTextView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.top.equalTo(self.titleLabel.mas_bottom).offset(theme.largeSpacing);
        make.left.equalTo(self.contentView).offset(theme.horizontalMargin);
        make.right.equalTo(self.contentView).offset(-theme.horizontalMargin);
        make.bottom.equalTo(self.contentView).offset(-theme.largeSpacing);
    }];
    
    // 关闭按钮 - 使用小尺寸
    self.closeButton = [[IDFAGradientButton alloc] initWithTitle:@"关闭" size:IDFAButtonSizeSmall];
    [self.closeButton addTarget:self action:@selector(closeButtonTapped) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:self.closeButton];
    [self.closeButton mas_makeConstraints:^(MASConstraintMaker *make) {
        make.bottom.equalTo(self.view.mas_safeAreaLayoutGuideBottom).offset(-theme.horizontalMargin);
        make.centerX.equalTo(self.view);
        make.width.equalTo(@160);
        make.height.equalTo(@([self.closeButton recommendedHeight]));
    }];
}

- (void)loadContent {
    NSString *fileName = @"";
    NSString *title = @"";

    // 获取当前语言
    IDFALanguageType currentLanguage = [IDFALocalizationManager sharedManager].currentLanguage;
    BOOL isChineseLanguage = (currentLanguage == IDFALanguageTypeSimplifiedChinese ||
                             currentLanguage == IDFALanguageTypeTraditionalChinese);

    switch (self.documentType) {
        case IDFADocumentTypeAbout:
            fileName = isChineseLanguage ? @"关于" : @"about_en";
            title = IDFALocalizedString(@"about");
            break;
        case IDFADocumentTypeUserAgreement:
            fileName = isChineseLanguage ? @"用户协议" : @"user_agreement_en";
            title = IDFALocalizedString(@"agreement");
            break;
        case IDFADocumentTypePrivacyPolicy:
            fileName = isChineseLanguage ? @"隐私政策" : @"privacy_policy_en";
            title = IDFALocalizedString(@"privacy");
            break;
    }

    self.titleLabel.text = title;
    
    // 显示加载提示
    [self showLoadingState];
    
    // 异步加载和处理文本内容
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        NSString *content = [self loadContentFromFile:fileName];
        NSAttributedString *attributedContent = [self createAttributedStringWithContent:content];
        
        // 回到主线程更新UI
        dispatch_async(dispatch_get_main_queue(), ^{
            [self hideLoadingState];
            [self updateContentWithAttributedString:attributedContent];
        });
    });
}

- (NSString *)loadContentFromFile:(NSString *)fileName {
    // 首先尝试加载HTML文件
    NSString *filePath = [[NSBundle mainBundle] pathForResource:fileName ofType:@"html"];

    // 如果HTML文件不存在，尝试加载TXT文件
    if (!filePath) {
        filePath = [[NSBundle mainBundle] pathForResource:fileName ofType:@"txt"];
    }

    NSString *content = @"";

    if (filePath) {
        NSError *error;
        content = [NSString stringWithContentsOfFile:filePath encoding:NSUTF8StringEncoding error:&error];
        if (error) {
            content = IDFALocalizedString(@"file_read_error");
        } else if ([filePath.pathExtension isEqualToString:@"html"]) {
            // 如果是HTML文件，提取纯文本内容
            content = [self extractTextFromHTML:content];
        }
    } else {
        content = IDFALocalizedString(@"file_not_found");
    }

    return content;
}

- (NSString *)extractTextFromHTML:(NSString *)htmlContent {
    // 简单的HTML标签移除方法
    NSString *text = htmlContent;

    // 移除HTML标签
    NSRegularExpression *regex = [NSRegularExpression regularExpressionWithPattern:@"<[^>]*>" options:0 error:nil];
    text = [regex stringByReplacingMatchesInString:text options:0 range:NSMakeRange(0, text.length) withTemplate:@""];

    // 解码HTML实体
    text = [text stringByReplacingOccurrencesOfString:@"&lt;" withString:@"<"];
    text = [text stringByReplacingOccurrencesOfString:@"&gt;" withString:@">"];
    text = [text stringByReplacingOccurrencesOfString:@"&amp;" withString:@"&"];
    text = [text stringByReplacingOccurrencesOfString:@"&quot;" withString:@"\""];
    text = [text stringByReplacingOccurrencesOfString:@"&#39;" withString:@"'"];
    text = [text stringByReplacingOccurrencesOfString:@"&nbsp;" withString:@" "];

    // 清理多余的空白字符
    text = [text stringByReplacingOccurrencesOfString:@"\n\n\n" withString:@"\n\n"];
    text = [text stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];

    return text;
}

- (NSAttributedString *)createAttributedStringWithContent:(NSString *)content {
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];
    
    // 优化段落样式创建
    NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
    paragraphStyle.lineSpacing = 6;
    paragraphStyle.alignment = NSTextAlignmentLeft;
    paragraphStyle.lineBreakMode = NSLineBreakByWordWrapping;
    
    // 创建属性字典
    NSDictionary *attributes = @{
        NSParagraphStyleAttributeName: paragraphStyle,
        NSFontAttributeName: [UIFont systemFontOfSize:theme.descriptionFontSize],
        NSForegroundColorAttributeName: theme.secondaryTextColor
    };
    
    // 如果内容很长，可以考虑分段处理
    if (content.length > 10000) {
        return [self createLongAttributedStringWithContent:content attributes:attributes];
    } else {
        return [[NSAttributedString alloc] initWithString:content attributes:attributes];
    }
}

- (NSAttributedString *)createLongAttributedStringWithContent:(NSString *)content attributes:(NSDictionary *)attributes {
    // 对于超长文本，使用 NSMutableAttributedString 逐段添加
    NSMutableAttributedString *mutableAttributedString = [[NSMutableAttributedString alloc] init];
    
    // 按段落分割文本
    NSArray *paragraphs = [content componentsSeparatedByString:@"\n"];
    
    for (NSInteger i = 0; i < paragraphs.count; i++) {
        NSString *paragraph = paragraphs[i];
        NSAttributedString *attributedParagraph = [[NSAttributedString alloc] initWithString:paragraph attributes:attributes];
        [mutableAttributedString appendAttributedString:attributedParagraph];
        
        // 添加换行符（除了最后一段）
        if (i < paragraphs.count - 1) {
            NSAttributedString *newLine = [[NSAttributedString alloc] initWithString:@"\n" attributes:attributes];
            [mutableAttributedString appendAttributedString:newLine];
        }
    }
    
    return [mutableAttributedString copy];
}

- (void)updateContentWithAttributedString:(NSAttributedString *)attributedString {
    // 使用 UITextView 设置富文本内容，性能更好
    self.contentTextView.attributedText = attributedString;
    
    // 强制布局更新
    [self.contentView setNeedsLayout];
    [self.contentView layoutIfNeeded];
}

- (void)showLoadingState {
    // 显示加载提示
    self.contentTextView.text = @"正在加载内容...";
    self.contentTextView.textAlignment = NSTextAlignmentCenter;
    self.contentTextView.alpha = 0.6;
    
    // 可以添加一个简单的动画效果
    [UIView animateWithDuration:0.5 delay:0 options:UIViewAnimationOptionRepeat | UIViewAnimationOptionAutoreverse animations:^{
        self.contentTextView.alpha = 0.3;
    } completion:nil];
}

- (void)hideLoadingState {
    // 停止动画并恢复状态
    [self.contentTextView.layer removeAllAnimations];
    self.contentTextView.alpha = 1.0;
    self.contentTextView.textAlignment = NSTextAlignmentLeft;
}

- (void)closeButtonTapped {
    [self dismissViewControllerAnimated:YES completion:nil];
}

@end
