# GETIDFA 多语言功能实现说明

## 概述

本项目已成功实现了多语言国际化功能，支持8种语言：
- 简体中文 (zh-Hans)
- 繁体中文 (zh-Hant) 
- 英文 (en)
- 日文 (ja)
- 韩文 (ko)
- 俄语 (ru)
- 泰语 (th)
- 越南语 (vi)

## 实现架构

### 1. 核心组件

#### IDFALocalizationManager
- 单例模式的多语言管理器
- 负责加载和管理本地化字符串
- 自动检测系统语言并设置默认语言
- 支持动态语言切换

#### Localizations.json
- 存储所有多语言文本的JSON文件
- 按照语言代码组织翻译内容
- 支持字符串和字符串数组两种数据类型

### 2. 使用方法

#### 基本用法
```objective-c
// 获取本地化字符串
NSString *text = IDFALocalizedString(@"welcome_title");

// 获取本地化字符串数组
NSArray *descriptions = IDFALocalizedStringArray(@"restricted_descriptions");
```

#### 语言切换
```objective-c
// 切换到指定语言
[[IDFALocalizationManager sharedManager] setLanguage:IDFALanguageTypeEnglish];
```

### 3. 文件结构

```
GETIDFA/
├── Resources/
│   └── Localizations.json          # 多语言配置文件
├── Utils/
│   ├── IDFALocalizationManager.h   # 多语言管理器头文件
│   ├── IDFALocalizationManager.m   # 多语言管理器实现
│   ├── IDFALanguageTestViewController.h  # 语言测试页面
│   └── IDFALanguageTestViewController.m  # 语言测试页面实现
└── ViewControllers/
    └── (各个页面已更新使用多语言)
```

### 4. 已国际化的内容

- 欢迎页面文本
- 所有状态页面标题
- 按钮文本（复制、分享、设置等）
- IDFA说明文本
- 操作步骤指导
- 错误提示信息
- 底部工具栏按钮

### 5. 语言回退机制

1. 优先使用当前设置的语言
2. 如果当前语言没有对应翻译，使用英文
3. 如果英文也没有，使用简体中文
4. 最后返回原始key作为兜底

### 6. 测试功能

项目包含了一个语言测试页面，可以：
- 实时切换语言
- 查看所有本地化字符串的效果
- 验证语言切换的即时性

通过底部工具栏的"Lang"按钮可以访问测试页面。

## 使用注意事项

1. 所有新增的UI文本都应该添加到Localizations.json中
2. 使用IDFALocalizedString宏来获取本地化字符串
3. 语言切换会发送通知，需要监听的页面应该订阅通知并更新UI
4. 保持中文文本不变，其他语言简洁明了
5. 系统会自动根据用户的系统语言设置默认语言

## 扩展说明

如需添加新语言：
1. 在IDFALanguageType枚举中添加新语言类型
2. 在languageCodeMap中添加语言代码映射
3. 在Localizations.json中为所有key添加新语言的翻译
4. 在setupLanguageFromSystem方法中添加系统语言检测逻辑
