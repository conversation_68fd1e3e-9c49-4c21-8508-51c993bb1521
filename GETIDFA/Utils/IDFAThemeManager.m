//
//  IDFAThemeManager.m
//  GETIDFA
//
//  Created by apple on 2025/6/13.
//

#import "IDFAThemeManager.h"

@interface IDFAThemeManager ()
@property (nonatomic, strong) NSDictionary *lightThemeConfig;
@property (nonatomic, strong) NSDictionary *darkThemeConfig;
@property (nonatomic, strong) NSDictionary *currentThemeConfig;
@end

@implementation IDFAThemeManager

+ (instancetype)sharedManager {
    static IDFAThemeManager *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[IDFAThemeManager alloc] init];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        [self setupThemeConfigs];
        _followSystemTheme = YES; // 默认跟随系统主题
        
        // 初始化主题
        if (_followSystemTheme) {
            _currentTheme = [self systemTheme];
        } else {
            _currentTheme = IDFAThemeTypeLight;
        }
        _currentThemeConfig = (_currentTheme == IDFAThemeTypeLight) ? _lightThemeConfig : _darkThemeConfig;
        
        // 启用系统主题追踪
        [self enableSystemThemeTracking];
    }
    return self;
}

- (void)dealloc {
    [self disableSystemThemeTracking];
}

- (void)setupThemeConfigs {
    // 亮色主题配置
    _lightThemeConfig = @{
        // 字体大小
        @"logoFontSize": @42,
        @"titleFontSize": @22,
        @"descriptionFontSize": @16,
        @"buttonFontSize": @16,
        @"smallButtonFontSize": @12,
        @"stepTitleFontSize": @16,
        @"stepNumberFontSize": @18,
        @"idfaValueFontSize": @14,
        @"hintFontSize": @12,
        
        // 颜色
        @"primaryTextColor": [UIColor whiteColor],
        @"secondaryTextColor": [UIColor colorWithWhite:0.9 alpha:1.0],
        @"successTextColor": [UIColor colorWithRed:0.2 green:0.8 blue:0.2 alpha:1.0],
        @"warningTextColor": [UIColor colorWithRed:1.0 green:0.6 blue:0.0 alpha:1.0],
        @"errorTextColor": [UIColor colorWithRed:1.0 green:0.3 blue:0.3 alpha:1.0],
        @"hintTextColor": [UIColor colorWithWhite:0.7 alpha:1.0],
        @"idfaTextColor": [UIColor colorWithRed:0.7 green:0.8 blue:1.0 alpha:1.0],
        
        // 背景颜色
        @"containerBackgroundColor": [UIColor colorWithWhite:1.0 alpha:0.12],
        @"stepContainerBackgroundColor": [UIColor colorWithWhite:1.0 alpha:0.1],
        @"borderColor": [UIColor colorWithWhite:1.0 alpha:0.3],
        
        // 渐变色
        @"gradientColors": @[
            [UIColor colorWithRed:0.4 green:0.6 blue:1.0 alpha:1.0],
            [UIColor colorWithRed:0.6 green:0.4 blue:1.0 alpha:1.0],
            [UIColor colorWithRed:0.8 green:0.3 blue:0.9 alpha:1.0]
        ],
        @"buttonGradientColors": @[
            [UIColor colorWithRed:0.3 green:0.5 blue:1.0 alpha:0.8],
            [UIColor colorWithRed:0.6 green:0.3 blue:0.9 alpha:0.8]
        ],
        
        // 尺寸
        @"containerCornerRadius": @16,
        @"buttonCornerRadius": @22,
        @"stepContainerCornerRadius": @16,
        @"borderWidth": @1.5,
        
        // 间距
        @"largeSpacing": @35,
        @"mediumSpacing": @25,
        @"smallSpacing": @15,
        @"horizontalMargin": @20,
        
        // 阴影
        @"shadowOffset": [NSValue valueWithCGSize:CGSizeMake(0, 3)],
        @"shadowRadius": @6,
        @"shadowOpacity": @0.3
    };
    
    // 暗色主题配置
    _darkThemeConfig = @{
        // 字体大小（与亮色相同）
        @"logoFontSize": @42,
        @"titleFontSize": @22,
        @"descriptionFontSize": @16,
        @"buttonFontSize": @16,
        @"smallButtonFontSize": @12,
        @"stepTitleFontSize": @16,
        @"stepNumberFontSize": @18,
        @"idfaValueFontSize": @14,
        @"hintFontSize": @12,
        
        // 颜色（优化的暗色主题）
        @"primaryTextColor": [UIColor colorWithWhite:0.95 alpha:1.0],
        @"secondaryTextColor": [UIColor colorWithWhite:0.78 alpha:1.0],
        @"successTextColor": [UIColor colorWithRed:0.2 green:0.85 blue:0.3 alpha:1.0],
        @"warningTextColor": [UIColor colorWithRed:1.0 green:0.65 blue:0.1 alpha:1.0],
        @"errorTextColor": [UIColor colorWithRed:1.0 green:0.35 blue:0.35 alpha:1.0],
        @"hintTextColor": [UIColor colorWithWhite:0.55 alpha:1.0],
        @"idfaTextColor": [UIColor colorWithRed:0.5 green:0.65 blue:0.9 alpha:1.0],
        
        // 背景颜色（优化的暗色主题）
        @"containerBackgroundColor": [UIColor colorWithRed:0.12 green:0.12 blue:0.18 alpha:0.85],
        @"stepContainerBackgroundColor": [UIColor colorWithRed:0.08 green:0.08 blue:0.12 alpha:0.7],
        @"borderColor": [UIColor colorWithRed:0.35 green:0.35 blue:0.45 alpha:0.6],
        
        // 渐变色（优化的暗色主题）
        @"gradientColors": @[
            [UIColor colorWithRed:0.1 green:0.15 blue:0.35 alpha:1.0],
            [UIColor colorWithRed:0.15 green:0.1 blue:0.45 alpha:1.0],
            [UIColor colorWithRed:0.25 green:0.05 blue:0.4 alpha:1.0]
        ],
        @"buttonGradientColors": @[
            [UIColor colorWithRed:0.15 green:0.25 blue:0.55 alpha:0.9],
            [UIColor colorWithRed:0.25 green:0.15 blue:0.5 alpha:0.9]
        ],
        
        // 尺寸（与亮色相同）
        @"containerCornerRadius": @16,
        @"buttonCornerRadius": @22,
        @"stepContainerCornerRadius": @16,
        @"borderWidth": @1.5,
        
        // 间距（与亮色相同）
        @"largeSpacing": @35,
        @"mediumSpacing": @25,
        @"smallSpacing": @15,
        @"horizontalMargin": @20,
        
        // 阴影（暗色主题更强）
        @"shadowOffset": [NSValue valueWithCGSize:CGSizeMake(0, 4)],
        @"shadowRadius": @8,
        @"shadowOpacity": @0.6
    };
}

- (void)switchToTheme:(IDFAThemeType)theme {
    _currentTheme = theme;
    _currentThemeConfig = (theme == IDFAThemeTypeLight) ? _lightThemeConfig : _darkThemeConfig;
    
    // 发送主题切换通知
    [[NSNotificationCenter defaultCenter] postNotificationName:@"IDFAThemeDidChangeNotification" object:nil];
}

#pragma mark - 系统主题适配

- (void)enableSystemThemeTracking {
    _followSystemTheme = YES;
    
    // 监听系统外观变化
    if (@available(iOS 13.0, *)) {
        [[NSNotificationCenter defaultCenter] addObserver:self
                                                 selector:@selector(systemAppearanceDidChange:)
                                                     name:UIApplicationDidBecomeActiveNotification
                                                   object:nil];
    }
    
    // 立即更新到系统主题
    [self updateToSystemTheme];
}

- (void)disableSystemThemeTracking {
    _followSystemTheme = NO;
    [[NSNotificationCenter defaultCenter] removeObserver:self name:UIApplicationDidBecomeActiveNotification object:nil];
}

- (IDFAThemeType)systemTheme {
    if (@available(iOS 13.0, *)) {
        UIUserInterfaceStyle style = UIScreen.mainScreen.traitCollection.userInterfaceStyle;
        return (style == UIUserInterfaceStyleDark) ? IDFAThemeTypeDark : IDFAThemeTypeLight;
    }
    return IDFAThemeTypeLight; // iOS 13 之前默认为亮色主题
}

- (void)systemAppearanceDidChange:(NSNotification *)notification {
    if (_followSystemTheme) {
        [self updateToSystemTheme];
    }
}

- (void)updateToSystemTheme {
    IDFAThemeType systemTheme = [self systemTheme];
    if (systemTheme != _currentTheme) {
        [self switchToTheme:systemTheme];
    }
}

// 字体大小
- (CGFloat)logoFontSize { return [_currentThemeConfig[@"logoFontSize"] floatValue]; }
- (CGFloat)titleFontSize { return [_currentThemeConfig[@"titleFontSize"] floatValue]; }
- (CGFloat)descriptionFontSize { return [_currentThemeConfig[@"descriptionFontSize"] floatValue]; }
- (CGFloat)buttonFontSize { return [_currentThemeConfig[@"buttonFontSize"] floatValue]; }
- (CGFloat)smallButtonFontSize{ return [_currentThemeConfig[@"smallButtonFontSize"] floatValue]; }
- (CGFloat)stepTitleFontSize { return [_currentThemeConfig[@"stepTitleFontSize"] floatValue]; }
- (CGFloat)stepNumberFontSize { return [_currentThemeConfig[@"stepNumberFontSize"] floatValue]; }
- (CGFloat)idfaValueFontSize { return [_currentThemeConfig[@"idfaValueFontSize"] floatValue]; }
- (CGFloat)hintFontSize { return [_currentThemeConfig[@"hintFontSize"] floatValue]; }

// 颜色
- (UIColor *)primaryTextColor { return _currentThemeConfig[@"primaryTextColor"]; }
- (UIColor *)secondaryTextColor { return _currentThemeConfig[@"secondaryTextColor"]; }
- (UIColor *)successTextColor { return _currentThemeConfig[@"successTextColor"]; }
- (UIColor *)warningTextColor { return _currentThemeConfig[@"warningTextColor"]; }
- (UIColor *)errorTextColor { return _currentThemeConfig[@"errorTextColor"]; }
- (UIColor *)hintTextColor { return _currentThemeConfig[@"hintTextColor"]; }
- (UIColor *)idfaTextColor { return _currentThemeConfig[@"idfaTextColor"]; }

// 背景颜色
- (UIColor *)containerBackgroundColor { return _currentThemeConfig[@"containerBackgroundColor"]; }
- (UIColor *)stepContainerBackgroundColor { return _currentThemeConfig[@"stepContainerBackgroundColor"]; }
- (UIColor *)borderColor { return _currentThemeConfig[@"borderColor"]; }

// 渐变色
- (NSArray<UIColor *> *)gradientColors { return _currentThemeConfig[@"gradientColors"]; }
- (NSArray<UIColor *> *)buttonGradientColors { return _currentThemeConfig[@"buttonGradientColors"]; }

// 尺寸
- (CGFloat)containerCornerRadius { return [_currentThemeConfig[@"containerCornerRadius"] floatValue]; }
- (CGFloat)buttonCornerRadius { return [_currentThemeConfig[@"buttonCornerRadius"] floatValue]; }
- (CGFloat)stepContainerCornerRadius { return [_currentThemeConfig[@"stepContainerCornerRadius"] floatValue]; }
- (CGFloat)borderWidth { return [_currentThemeConfig[@"borderWidth"] floatValue]; }

// 间距
- (CGFloat)largeSpacing { return [_currentThemeConfig[@"largeSpacing"] floatValue]; }
- (CGFloat)mediumSpacing { return [_currentThemeConfig[@"mediumSpacing"] floatValue]; }
- (CGFloat)smallSpacing { return [_currentThemeConfig[@"smallSpacing"] floatValue]; }
- (CGFloat)horizontalMargin { return [_currentThemeConfig[@"horizontalMargin"] floatValue]; }

// 阴影
- (CGSize)shadowOffset { return [_currentThemeConfig[@"shadowOffset"] CGSizeValue]; }
- (CGFloat)shadowRadius { return [_currentThemeConfig[@"shadowRadius"] floatValue]; }
- (CGFloat)shadowOpacity { return [_currentThemeConfig[@"shadowOpacity"] floatValue]; }

@end
