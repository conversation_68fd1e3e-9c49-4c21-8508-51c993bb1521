//
//  IDFALocalizationManager.m
//  GETIDFA
//
//  Created by apple on 2025/6/16.
//

#import "IDFALocalizationManager.h"

@interface IDFALocalizationManager ()
@property (nonatomic, strong) NSDictionary *localizationData;
@property (nonatomic, strong) NSDictionary *languageCodeMap;
@end

@implementation IDFALocalizationManager

+ (instancetype)sharedManager {
    static IDFALocalizationManager *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[IDFALocalizationManager alloc] init];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        [self setupLanguageCodeMap];
        [self loadLocalizationData];
        [self setupLanguageFromSystem];
    }
    return self;
}

- (void)setupLanguageCodeMap {
    self.languageCodeMap = @{
        @(IDFALanguageTypeSimplifiedChinese): @"zh-Hans",
        @(IDFALanguageTypeTraditionalChinese): @"zh-Hant",
        @(IDFALanguageTypeEnglish): @"en",
        @(IDFALanguageTypeJapanese): @"ja",
        @(IDFALanguageTypeKorean): @"ko",
        @(IDFALanguageTypeRussian): @"ru",
        @(IDFALanguageTypeThai): @"th",
        @(IDFALanguageTypeVietnamese): @"vi"
    };
}

- (void)loadLocalizationData {
    NSString *filePath = [[NSBundle mainBundle] pathForResource:@"Localizations" ofType:@"json"];
    if (filePath) {
        NSData *data = [NSData dataWithContentsOfFile:filePath];
        if (data) {
            NSError *error;
            self.localizationData = [NSJSONSerialization JSONObjectWithData:data options:0 error:&error];
            if (error) {
                NSLog(@"Error loading localization data: %@", error.localizedDescription);
                self.localizationData = @{};
            }
        }
    } else {
        NSLog(@"Localizations.json file not found");
        self.localizationData = @{};
    }
}

- (void)setupLanguageFromSystem {
    NSString *systemLanguage = [[NSLocale preferredLanguages] firstObject];
    
    // 根据系统语言设置默认语言
    if ([systemLanguage hasPrefix:@"zh-Hans"] || [systemLanguage hasPrefix:@"zh-CN"]) {
        self.currentLanguage = IDFALanguageTypeSimplifiedChinese;
    } else if ([systemLanguage hasPrefix:@"zh-Hant"] || [systemLanguage hasPrefix:@"zh-TW"] || [systemLanguage hasPrefix:@"zh-HK"]) {
        self.currentLanguage = IDFALanguageTypeTraditionalChinese;
    } else if ([systemLanguage hasPrefix:@"ja"]) {
        self.currentLanguage = IDFALanguageTypeJapanese;
    } else if ([systemLanguage hasPrefix:@"ko"]) {
        self.currentLanguage = IDFALanguageTypeKorean;
    } else if ([systemLanguage hasPrefix:@"ru"]) {
        self.currentLanguage = IDFALanguageTypeRussian;
    } else if ([systemLanguage hasPrefix:@"th"]) {
        self.currentLanguage = IDFALanguageTypeThai;
    } else if ([systemLanguage hasPrefix:@"vi"]) {
        self.currentLanguage = IDFALanguageTypeVietnamese;
    } else {
        // 默认使用英文
        self.currentLanguage = IDFALanguageTypeEnglish;
    }
}

- (NSString *)localizedStringForKey:(NSString *)key {
    if (!key || !self.localizationData) {
        return key ?: @"";
    }
    
    NSDictionary *keyData = self.localizationData[key];
    if (!keyData || ![keyData isKindOfClass:[NSDictionary class]]) {
        return key;
    }
    
    NSString *languageCode = [self currentLanguageCode];
    NSString *localizedString = keyData[languageCode];
    
    // 如果当前语言没有对应的翻译，尝试使用英文
    if (!localizedString && self.currentLanguage != IDFALanguageTypeEnglish) {
        localizedString = keyData[@"en"];
    }
    
    // 如果英文也没有，使用简体中文
    if (!localizedString) {
        localizedString = keyData[@"zh-Hans"];
    }
    
    return localizedString ?: key;
}

- (NSArray<NSString *> *)localizedStringArrayForKey:(NSString *)key {
    if (!key || !self.localizationData) {
        return @[];
    }
    
    NSDictionary *keyData = self.localizationData[key];
    if (!keyData || ![keyData isKindOfClass:[NSDictionary class]]) {
        return @[];
    }
    
    NSString *languageCode = [self currentLanguageCode];
    NSArray *localizedArray = keyData[languageCode];
    
    // 如果当前语言没有对应的翻译，尝试使用英文
    if (!localizedArray && self.currentLanguage != IDFALanguageTypeEnglish) {
        localizedArray = keyData[@"en"];
    }
    
    // 如果英文也没有，使用简体中文
    if (!localizedArray) {
        localizedArray = keyData[@"zh-Hans"];
    }
    
    return localizedArray ?: @[];
}

- (void)setLanguage:(IDFALanguageType)language {
    self.currentLanguage = language;
    
    // 发送语言变更通知
    [[NSNotificationCenter defaultCenter] postNotificationName:@"IDFALanguageDidChangeNotification" object:nil];
}

- (NSString *)currentLanguageCode {
    return self.languageCodeMap[@(self.currentLanguage)] ?: @"en";
}

@end
