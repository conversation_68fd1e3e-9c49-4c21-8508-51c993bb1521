//
//  IDFALocalizationManager.h
//  GETIDFA
//
//  Created by apple on 2025/6/16.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, IDFALanguageType) {
    IDFALanguageTypeSimplifiedChinese,  // zh-<PERSON>ALanguageTypeTraditionalChinese, // zh-<PERSON>t
    IDFALanguageTypeEnglish,            // en
    IDFALanguageTypeJapanese,           // ja
    IDFALanguageTypeKorean,             // ko
    IDFALanguageTypeRussian,            // ru
    IDFALanguageTypeThai,               // th
    IDFALanguageTypeVietnamese          // vi
};

@interface IDFALocalizationManager : NSObject

@property (nonatomic, assign) IDFALanguageType currentLanguage;

+ (instancetype)sharedManager;

// 获取本地化字符串
- (NSString *)localizedStringForKey:(NSString *)key;

// 获取本地化字符串数组
- (NSArray<NSString *> *)localizedStringArrayForKey:(NSString *)key;

// 设置语言
- (void)setLanguage:(IDFALanguageType)language;

// 获取当前语言代码
- (NSString *)currentLanguageCode;

// 根据系统语言自动设置
- (void)setupLanguageFromSystem;

// 便捷宏定义
#define IDFALocalizedString(key) [[IDFALocalizationManager sharedManager] localizedStringForKey:key]
#define IDFALocalizedStringArray(key) [[IDFALocalizationManager sharedManager] localizedStringArrayForKey:key]

@end

NS_ASSUME_NONNULL_END
