//
//  TrackingStatusManager.m
//  GETIDFA
//
//  Created by apple on 2025/6/12.
//

#import "TrackingStatusManager.h"
#import <UIKit/UIKit.h>

static NSString * const kHasRequestedKey = @"HasRequestedTrackingAuthorization";

@implementation TrackingStatusManager

+ (instancetype)sharedManager {
    static TrackingStatusManager *inst;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        inst = [[TrackingStatusManager alloc] init];
    });
    return inst;
}

// 读取或写入 NSUserDefaults 标记
- (BOOL)hasRequestedBefore {
    return [[NSUserDefaults standardUserDefaults] boolForKey:kHasRequestedKey];
}
- (void)markRequested {
    [[NSUserDefaults standardUserDefaults] setBool:YES forKey:kHasRequestedKey];
    [[NSUserDefaults standardUserDefaults] synchronize];
}

// 获取当前 ATT 状态并映射为 TrackingStatus，不写标记、不请求
- (TrackingStatus)currentTrackingStatus {
    if (@available(iOS 14, *)) {
        ATTrackingManagerAuthorizationStatus status = [ATTrackingManager trackingAuthorizationStatus];
        switch (status) {
            case ATTrackingManagerAuthorizationStatusAuthorized:
                return TrackingStatusAuthorized;
            case ATTrackingManagerAuthorizationStatusDenied: {
                // Denied 情况下，需区分是否曾请求过
                if ([self hasRequestedBefore]) {
                    return TrackingStatusDeniedAppOff;
                } else {
                    return TrackingStatusDeniedGlobalOff;
                }
            }
            case ATTrackingManagerAuthorizationStatusRestricted:
                return TrackingStatusRestricted;
            case ATTrackingManagerAuthorizationStatusNotDetermined:
                return TrackingStatusNotDetermined;
            default:
                return TrackingStatusUnavailable;
        }
    } else {
        // iOS < 14，可按需处理；此处简单映射
        if ([[ASIdentifierManager sharedManager] respondsToSelector:@selector(isAdvertisingTrackingEnabled)]) {
            BOOL enabled = [[ASIdentifierManager sharedManager] isAdvertisingTrackingEnabled];
            return enabled ? TrackingStatusAuthorized : TrackingStatusDeniedAppOff;
        }
        return TrackingStatusUnavailable;
    }
}

// 主流程：根据当前状态决定是否请求或直接返回
- (void)requestOrCheckTrackingWithCompletion:(void(^)(TrackingStatus status))completion {
    if (@available(iOS 14, *)) {
        ATTrackingManagerAuthorizationStatus status = [ATTrackingManager trackingAuthorizationStatus];
        if (status == ATTrackingManagerAuthorizationStatusNotDetermined) {
            // 首次请求：记录标记，然后弹窗请求
            [self markRequested];
            [ATTrackingManager requestTrackingAuthorizationWithCompletionHandler:^(ATTrackingManagerAuthorizationStatus newStatus) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    TrackingStatus ts;
                    switch (newStatus) {
                        case ATTrackingManagerAuthorizationStatusAuthorized:
                            ts = TrackingStatusAuthorized;
                            break;
                        case ATTrackingManagerAuthorizationStatusDenied:
                            // 这里因为已 markRequested，属于用户拒绝
                            ts = TrackingStatusDeniedAppOff;
                            break;
                        case ATTrackingManagerAuthorizationStatusRestricted:
                            ts = TrackingStatusRestricted;
                            break;
                        case ATTrackingManagerAuthorizationStatusNotDetermined:
                            ts = TrackingStatusNotDetermined;
                            break;
                        default:
                            ts = TrackingStatusUnavailable;
                            break;
                    }
                    if (completion) completion(ts);
                });
            }];
        } else {
            // 非首次，直接映射当前状态
            TrackingStatus ts = [self currentTrackingStatus];
            if (completion) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    completion(ts);
                });
            }
        }
    } else {
        // iOS < 14：直接返回当前状态
        TrackingStatus ts = [self currentTrackingStatus];
        if (completion) {
            dispatch_async(dispatch_get_main_queue(), ^{
                completion(ts);
            });
        }
    }
}

@end
