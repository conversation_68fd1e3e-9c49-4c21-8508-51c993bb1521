//
//  IDFALanguageTestViewController.m
//  GETIDFA
//
//  Created by apple on 2025/6/16.
//

#import "IDFALanguageTestViewController.h"
#import "IDFALocalizationManager.h"
#import "IDFAThemeManager.h"
#import "Masonry.h"

@interface IDFALanguageTestViewController ()
@property (nonatomic, strong) UIScrollView *scrollView;
@property (nonatomic, strong) UIStackView *stackView;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) NSMutableArray<UILabel *> *testLabels;
@property (nonatomic, strong) NSMutableArray<UIButton *> *languageButtons;
@end

@implementation IDFALanguageTestViewController

- (void)viewDidLoad {
    [super viewDidLoad];
    
    self.testLabels = [NSMutableArray array];
    self.languageButtons = [NSMutableArray array];
    
    [self setupUI];
    [self setupLanguageButtons];
    [self setupTestLabels];
    [self updateAllTexts];
    
    // 监听语言变更通知
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(languageDidChange:)
                                                 name:@"IDFALanguageDidChangeNotification"
                                               object:nil];
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)setupUI {
    self.view.backgroundColor = [UIColor systemBackgroundColor];
    
    // 导航栏
    self.navigationItem.title = @"Language Test";
    self.navigationItem.leftBarButtonItem = [[UIBarButtonItem alloc] initWithBarButtonSystemItem:UIBarButtonSystemItemClose target:self action:@selector(closeButtonTapped)];
    
    // 滚动视图
    self.scrollView = [[UIScrollView alloc] init];
    [self.view addSubview:self.scrollView];
    [self.scrollView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.view.mas_safeAreaLayoutGuide);
    }];
    
    // 堆栈视图
    self.stackView = [[UIStackView alloc] init];
    self.stackView.axis = UILayoutConstraintAxisVertical;
    self.stackView.spacing = 20;
    self.stackView.alignment = UIStackViewAlignmentFill;
    [self.scrollView addSubview:self.stackView];
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self.scrollView).insets(UIEdgeInsetsMake(20, 20, 20, 20));
        make.width.equalTo(self.scrollView).offset(-40);
    }];
    
    // 标题
    self.titleLabel = [[UILabel alloc] init];
    self.titleLabel.font = [UIFont boldSystemFontOfSize:24];
    self.titleLabel.textAlignment = NSTextAlignmentCenter;
    self.titleLabel.numberOfLines = 0;
    [self.stackView addArrangedSubview:self.titleLabel];
}

- (void)setupLanguageButtons {
    UILabel *sectionLabel = [[UILabel alloc] init];
    sectionLabel.text = @"Select Language:";
    sectionLabel.font = [UIFont boldSystemFontOfSize:18];
    [self.stackView addArrangedSubview:sectionLabel];
    
    NSArray *languages = @[
        @[@"简体中文", @(IDFALanguageTypeSimplifiedChinese)],
        @[@"繁體中文", @(IDFALanguageTypeTraditionalChinese)],
        @[@"English", @(IDFALanguageTypeEnglish)],
        @[@"日本語", @(IDFALanguageTypeJapanese)],
        @[@"한국어", @(IDFALanguageTypeKorean)],
        @[@"Русский", @(IDFALanguageTypeRussian)],
        @[@"ไทย", @(IDFALanguageTypeThai)],
        @[@"Tiếng Việt", @(IDFALanguageTypeVietnamese)]
    ];
    
    for (NSArray *languageInfo in languages) {
        UIButton *button = [UIButton buttonWithType:UIButtonTypeSystem];
        [button setTitle:languageInfo[0] forState:UIControlStateNormal];
        button.titleLabel.font = [UIFont systemFontOfSize:16];
        button.backgroundColor = [UIColor systemBlueColor];
        [button setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
        button.layer.cornerRadius = 8;
        button.tag = [languageInfo[1] integerValue];
        [button addTarget:self action:@selector(languageButtonTapped:) forControlEvents:UIControlEventTouchUpInside];
        
        [button mas_makeConstraints:^(MASConstraintMaker *make) {
            make.height.equalTo(@44);
        }];
        
        [self.stackView addArrangedSubview:button];
        [self.languageButtons addObject:button];
    }
}

- (void)setupTestLabels {
    UILabel *sectionLabel = [[UILabel alloc] init];
    sectionLabel.text = @"Localized Strings:";
    sectionLabel.font = [UIFont boldSystemFontOfSize:18];
    [self.stackView addArrangedSubview:sectionLabel];
    
    NSArray *testKeys = @[
        @"welcome_title",
        @"agree_continue",
        @"authorized_title",
        @"request_permission_title",
        @"what_is_idfa",
        @"idfa_description_1",
        @"copy",
        @"share",
        @"about",
        @"privacy"
    ];
    
    for (NSString *key in testKeys) {
        UIView *container = [[UIView alloc] init];
        container.backgroundColor = [UIColor systemGray6Color];
        container.layer.cornerRadius = 8;
        
        UILabel *keyLabel = [[UILabel alloc] init];
        keyLabel.text = [NSString stringWithFormat:@"Key: %@", key];
        keyLabel.font = [UIFont boldSystemFontOfSize:14];
        keyLabel.textColor = [UIColor systemBlueColor];
        
        UILabel *valueLabel = [[UILabel alloc] init];
        valueLabel.font = [UIFont systemFontOfSize:16];
        valueLabel.numberOfLines = 0;
        valueLabel.textColor = [UIColor labelColor];
        
        // 存储key到tag中
        valueLabel.tag = [testKeys indexOfObject:key];
        
        [container addSubview:keyLabel];
        [container addSubview:valueLabel];
        
        [keyLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.left.right.equalTo(container).insets(UIEdgeInsetsMake(8, 12, 0, 12));
        }];
        
        [valueLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.top.equalTo(keyLabel.mas_bottom).offset(4);
            make.left.right.bottom.equalTo(container).insets(UIEdgeInsetsMake(0, 12, 8, 12));
        }];
        
        [self.stackView addArrangedSubview:container];
        [self.testLabels addObject:valueLabel];
    }
}

- (void)languageButtonTapped:(UIButton *)sender {
    IDFALanguageType language = (IDFALanguageType)sender.tag;
    [[IDFALocalizationManager sharedManager] setLanguage:language];
}

- (void)languageDidChange:(NSNotification *)notification {
    [self updateAllTexts];
}

- (void)updateAllTexts {
    // 更新标题
    self.titleLabel.text = [NSString stringWithFormat:@"Current Language: %@", [[IDFALocalizationManager sharedManager] currentLanguageCode]];
    
    // 更新测试标签
    NSArray *testKeys = @[
        @"welcome_title",
        @"agree_continue", 
        @"authorized_title",
        @"request_permission_title",
        @"what_is_idfa",
        @"idfa_description_1",
        @"copy",
        @"share",
        @"about",
        @"privacy"
    ];
    
    for (NSInteger i = 0; i < self.testLabels.count && i < testKeys.count; i++) {
        UILabel *label = self.testLabels[i];
        NSString *key = testKeys[i];
        label.text = IDFALocalizedString(key);
    }
    
    // 更新当前选中的语言按钮
    IDFALanguageType currentLanguage = [IDFALocalizationManager sharedManager].currentLanguage;
    for (UIButton *button in self.languageButtons) {
        if (button.tag == currentLanguage) {
            button.backgroundColor = [UIColor systemGreenColor];
        } else {
            button.backgroundColor = [UIColor systemBlueColor];
        }
    }
}

- (void)closeButtonTapped {
    [self dismissViewControllerAnimated:YES completion:nil];
}

@end
