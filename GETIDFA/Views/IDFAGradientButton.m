//
//  IDFAGradientButton.m
//  GETIDFA
//
//  Created by apple on 2025/6/13.
//

#import "IDFAGradientButton.h"
#import "IDFAThemeManager.h"

@interface IDFAGradientButton ()
@property (nonatomic, strong) CAGradientLayer *gradientLayer;
@property (nonatomic, strong) CALayer *highlightLayer;
@property (nonatomic, assign) IDFAButtonSize buttonSize;
@end

@implementation IDFAGradientButton

- (instancetype)initWithTitle:(NSString *)title {
    return [self initWithTitle:title size:IDFAButtonSizeNormal];
}

- (instancetype)initWithTitle:(NSString *)title size:(IDFAButtonSize)size {
    self = [IDFAGradientButton buttonWithType:UIButtonTypeCustom];
    if (self) {
        _buttonSize = size;
        [self setTitle:title forState:UIControlStateNormal];
        [self setupButton];
        [self setupThemeObserver];
    }
    return self;
}

- (void)setupThemeObserver {
    [[NSNotificationCenter defaultCenter] addObserver:self 
                                             selector:@selector(themeDidChange:) 
                                                 name:@"IDFAThemeDidChangeNotification" 
                                               object:nil];
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)themeDidChange:(NSNotification *)notification {
    [self updateThemeAppearance];
}

- (void)setupButton {
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];

    // 根据按钮尺寸设置字体大小和圆角
    CGFloat fontSize = theme.buttonFontSize;
    CGFloat cornerRadius = theme.buttonCornerRadius;

    switch (self.buttonSize) {
        case IDFAButtonSizeNormal:
            fontSize = theme.buttonFontSize;
            cornerRadius = theme.buttonCornerRadius;
            break;
        case IDFAButtonSizeSmall:
            fontSize = theme.buttonFontSize - 2;
            cornerRadius = theme.buttonCornerRadius - 4;
            break;
        case IDFAButtonSizeMini:
            fontSize = theme.buttonFontSize - 4;
            cornerRadius = theme.buttonCornerRadius - 8;
            break;
    }

    // 设置文字样式
    [self setTitleColor:theme.primaryTextColor forState:UIControlStateNormal];
    [self setTitleColor:[theme.primaryTextColor colorWithAlphaComponent:0.8] forState:UIControlStateHighlighted];
    self.titleLabel.font = [UIFont boldSystemFontOfSize:fontSize];

    // 设置圆角
    self.layer.cornerRadius = cornerRadius;
    self.layer.masksToBounds = YES;
    
    // 创建渐变背景
    [self setupGradientBackground];
    
    // 添加玻璃效果边框
    self.layer.borderWidth = 1.0;
    self.layer.borderColor = [UIColor colorWithWhite:1.0 alpha:0.4].CGColor;

    // 添加柔和的阴影
    self.layer.shadowColor = [UIColor blackColor].CGColor;
    self.layer.shadowOffset = CGSizeMake(0, 2);
    self.layer.shadowRadius = 4;
    self.layer.shadowOpacity = 0.15;  // 更轻的阴影
    
    // 添加触摸动画
    [self addTarget:self action:@selector(touchDown) forControlEvents:UIControlEventTouchDown];
    [self addTarget:self action:@selector(touchUp) forControlEvents:UIControlEventTouchUpInside | UIControlEventTouchUpOutside | UIControlEventTouchCancel];
}

- (void)setupGradientBackground {
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];

    // 移除旧的渐变层
    if (self.gradientLayer) {
        [self.gradientLayer removeFromSuperlayer];
    }

    // 根据按钮尺寸设置圆角
    CGFloat cornerRadius = theme.buttonCornerRadius;
    switch (self.buttonSize) {
        case IDFAButtonSizeNormal:
            cornerRadius = theme.buttonCornerRadius;
            break;
        case IDFAButtonSizeSmall:
            cornerRadius = theme.buttonCornerRadius - 4;
            break;
        case IDFAButtonSizeMini:
            cornerRadius = theme.buttonCornerRadius - 8;
            break;
    }

    // 创建玻璃效果的渐变层
    self.gradientLayer = [CAGradientLayer layer];

    // 使用更轻盈的玻璃效果颜色 - 模拟毛玻璃效果
    NSArray *glassColors = @[
        (id)[UIColor colorWithWhite:1.0 alpha:0.3].CGColor,   // 顶部：白色半透明
        (id)[UIColor colorWithWhite:0.95 alpha:0.2].CGColor,  // 中上：微带灰的白色
        (id)[UIColor colorWithWhite:0.9 alpha:0.15].CGColor,  // 中下：更透明的灰白色
        (id)[UIColor colorWithWhite:0.8 alpha:0.08].CGColor,  // 底部：淡灰色半透明
    ];

    self.gradientLayer.colors = glassColors;
    self.gradientLayer.startPoint = CGPointMake(0, 0);
    self.gradientLayer.endPoint = CGPointMake(0, 1);  // 垂直渐变更符合玻璃效果
    self.gradientLayer.cornerRadius = cornerRadius;

    [self.layer insertSublayer:self.gradientLayer atIndex:0];

    // 添加顶部高光层增强玻璃效果
    [self setupHighlightLayer:cornerRadius];
}

- (void)layoutSubviews {
    [super layoutSubviews];
    // 更新渐变层的frame
    self.gradientLayer.frame = self.bounds;

    // 更新高光层的frame
    if (self.highlightLayer) {
        CGFloat highlightHeight = self.bounds.size.height * 0.4; // 高光占按钮高度的40%
        self.highlightLayer.frame = CGRectMake(0, 0, self.bounds.size.width, highlightHeight);

        // 更新高光层内部渐变的frame
        if (self.highlightLayer.sublayers.count > 0) {
            CAGradientLayer *highlightGradient = (CAGradientLayer *)self.highlightLayer.sublayers.firstObject;
            highlightGradient.frame = self.highlightLayer.bounds;
        }
    }
}

- (void)updateThemeAppearance {
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];

    // 根据按钮尺寸设置字体大小和圆角
    CGFloat fontSize = theme.buttonFontSize;
    CGFloat cornerRadius = theme.buttonCornerRadius;

    switch (self.buttonSize) {
        case IDFAButtonSizeNormal:
            fontSize = theme.buttonFontSize;
            cornerRadius = theme.buttonCornerRadius;
            break;
        case IDFAButtonSizeSmall:
            fontSize = theme.buttonFontSize - 2;
            cornerRadius = theme.buttonCornerRadius - 4;
            break;
        case IDFAButtonSizeMini:
            fontSize = theme.buttonFontSize - 4;
            cornerRadius = theme.buttonCornerRadius - 8;
            break;
    }

    // 更新文字颜色
    [self setTitleColor:theme.primaryTextColor forState:UIControlStateNormal];
    [self setTitleColor:[theme.primaryTextColor colorWithAlphaComponent:0.8] forState:UIControlStateHighlighted];
    self.titleLabel.font = [UIFont boldSystemFontOfSize:fontSize];

    // 更新圆角
    self.layer.cornerRadius = cornerRadius;
    self.gradientLayer.cornerRadius = cornerRadius;

    // 更新玻璃效果边框
    self.layer.borderWidth = 1.0;
    self.layer.borderColor = [UIColor colorWithWhite:1.0 alpha:0.4].CGColor;

    // 更新柔和阴影
    self.layer.shadowOffset = CGSizeMake(0, 2);
    self.layer.shadowRadius = 4;
    self.layer.shadowOpacity = 0.15;

    // 重新设置渐变背景
    [self setupGradientBackground];
}

#pragma mark - Touch Animation

- (void)touchDown {
    [UIView animateWithDuration:0.1 animations:^{
        self.transform = CGAffineTransformMakeScale(0.95, 0.95);
        self.alpha = 0.8;
    }];
}

- (void)touchUp {
    [UIView animateWithDuration:0.1 animations:^{
        self.transform = CGAffineTransformIdentity;
        self.alpha = 1.0;
    }];
}

#pragma mark - Convenience Methods

+ (CGFloat)recommendedHeightForSize:(IDFAButtonSize)size {
    switch (size) {
        case IDFAButtonSizeNormal:
            return 44.0;
        case IDFAButtonSizeSmall:
            return 36.0;
        case IDFAButtonSizeMini:
            return 28.0;
    }
}

- (CGFloat)recommendedHeight {
    return [IDFAGradientButton recommendedHeightForSize:self.buttonSize];
}

- (void)setupHighlightLayer:(CGFloat)cornerRadius {
    // 移除旧的高光层
    if (self.highlightLayer) {
        [self.highlightLayer removeFromSuperlayer];
    }

    // 创建高光层
    self.highlightLayer = [CALayer layer];
    self.highlightLayer.backgroundColor = [UIColor colorWithWhite:1.0 alpha:0.3].CGColor;
    self.highlightLayer.cornerRadius = cornerRadius;
    self.highlightLayer.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner; // 只有顶部圆角

    // 添加渐变效果让高光更自然
    CAGradientLayer *highlightGradient = [CAGradientLayer layer];
    highlightGradient.colors = @[
        (id)[UIColor colorWithWhite:1.0 alpha:0.4].CGColor,  // 顶部更亮
        (id)[UIColor colorWithWhite:1.0 alpha:0.0].CGColor   // 底部透明
    ];
    highlightGradient.startPoint = CGPointMake(0, 0);
    highlightGradient.endPoint = CGPointMake(0, 1);
    highlightGradient.cornerRadius = cornerRadius;
    highlightGradient.maskedCorners = kCALayerMinXMinYCorner | kCALayerMaxXMinYCorner;

    [self.highlightLayer addSublayer:highlightGradient];
    [self.layer insertSublayer:self.highlightLayer atIndex:1]; // 在渐变层之上
}

@end
