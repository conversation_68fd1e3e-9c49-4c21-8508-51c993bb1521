//
//  IDFADescriptionView.m
//  GETIDFA
//
//  Created by apple on 2025/6/13.
//

#import "IDFADescriptionView.h"
#import "IDFAThemeManager.h"
#import "Masonry.h"

@interface IDFADescriptionView ()
@property (nonatomic, strong) UIStackView *stackView;
@end

@implementation IDFADescriptionView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupUI];
        [self setupThemeObserver];
    }
    return self;
}

- (void)setupThemeObserver {
    [[NSNotificationCenter defaultCenter] addObserver:self 
                                             selector:@selector(themeDidChange:) 
                                                 name:@"IDFAThemeDidChangeNotification" 
                                               object:nil];
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)themeDidChange:(NSNotification *)notification {
    [self updateThemeAppearance];
}

- (void)setupUI {
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];
    
    // 设置背景
    self.backgroundColor = theme.containerBackgroundColor;
    self.layer.cornerRadius = theme.containerCornerRadius;
    self.layer.borderWidth = theme.borderWidth;
    self.layer.borderColor = theme.borderColor.CGColor;
    
    // 添加阴影
    self.layer.shadowColor = [UIColor blackColor].CGColor;
    self.layer.shadowOffset = CGSizeMake(0, 2);
    self.layer.shadowRadius = 4;
    self.layer.shadowOpacity = 0.25;
    self.layer.masksToBounds = NO;
    
    // 创建堆栈视图
    self.stackView = [[UIStackView alloc] init];
    self.stackView.axis = UILayoutConstraintAxisVertical;
    self.stackView.spacing = 8;
    self.stackView.alignment = UIStackViewAlignmentFill;
    self.stackView.distribution = UIStackViewDistributionFill;
    [self addSubview:self.stackView];
    
    [self.stackView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.edges.equalTo(self).insets(UIEdgeInsetsMake(15, 15, 15, 15));
    }];
}

- (void)setupWithDescriptions:(NSArray<NSString *> *)descriptions {
    // 清除现有视图
    for (UIView *subview in self.stackView.arrangedSubviews) {
        [self.stackView removeArrangedSubview:subview];
        [subview removeFromSuperview];
    }
    
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];
    
    // 添加每个描述项
    for (NSInteger i = 0; i < descriptions.count; i++) {
        NSString *description = descriptions[i];
        
        UIView *itemView = [self createDescriptionItemWithText:description isFirst:(i == 0)];
        [self.stackView addArrangedSubview:itemView];
    }
}

- (UIView *)createDescriptionItemWithText:(NSString *)text isFirst:(BOOL)isFirst {
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];
    
    UIView *itemView = [[UIView alloc] init];
    
    if (isFirst) {
        // 第一项是标题
        UILabel *titleLabel = [[UILabel alloc] init];
        titleLabel.text = text;
        titleLabel.font = [UIFont boldSystemFontOfSize:16];
        titleLabel.textColor = theme.primaryTextColor;
        titleLabel.textAlignment = NSTextAlignmentCenter;
        titleLabel.numberOfLines = 0;
        
        // 添加阴影
        titleLabel.layer.shadowColor = [UIColor blackColor].CGColor;
        titleLabel.layer.shadowOffset = CGSizeMake(1, 1);
        titleLabel.layer.shadowRadius = 2;
        titleLabel.layer.shadowOpacity = 0.3;
        
        [itemView addSubview:titleLabel];
        [titleLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.equalTo(itemView);
            make.height.greaterThanOrEqualTo(@25);
        }];
    } else {
        // 其他项是描述
        UIView *bulletContainer = [[UIView alloc] init];
        [itemView addSubview:bulletContainer];
        
        // 圆点
        UIView *bulletView = [[UIView alloc] init];
        bulletView.backgroundColor = theme.secondaryTextColor;
        bulletView.layer.cornerRadius = 2.5;
        [bulletContainer addSubview:bulletView];
        [bulletView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(bulletContainer);
            make.top.equalTo(bulletContainer).offset(6);
            make.width.height.equalTo(@5);
        }];
        
        // 描述文字
        UILabel *descLabel = [[UILabel alloc] init];
        descLabel.text = text;
        descLabel.font = [UIFont systemFontOfSize:13];
        descLabel.textColor = theme.secondaryTextColor;
        descLabel.numberOfLines = 0;
        
        // 设置行间距
        NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
        paragraphStyle.lineSpacing = 1;
        NSAttributedString *attributedString = [[NSAttributedString alloc] 
            initWithString:text 
            attributes:@{NSParagraphStyleAttributeName: paragraphStyle}];
        descLabel.attributedText = attributedString;
        
        [bulletContainer addSubview:descLabel];
        [descLabel mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(bulletView.mas_right).offset(8);
            make.top.bottom.right.equalTo(bulletContainer);
        }];
        
        [bulletContainer mas_makeConstraints:^(MASConstraintMaker *make) {
            make.edges.equalTo(itemView);
            make.height.greaterThanOrEqualTo(@20);
        }];
    }
    
    return itemView;
}

- (void)updateThemeAppearance {
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];
    
    self.backgroundColor = theme.containerBackgroundColor;
    self.layer.borderColor = theme.borderColor.CGColor;
    
    // 更新所有子视图的颜色
    for (UIView *arrangedSubview in self.stackView.arrangedSubviews) {
        for (UIView *subview in arrangedSubview.subviews) {
            if ([subview isKindOfClass:[UILabel class]]) {
                UILabel *label = (UILabel *)subview;
                if ([label.font.fontName containsString:@"Bold"]) {
                    label.textColor = theme.primaryTextColor;
                } else {
                    label.textColor = theme.secondaryTextColor;
                }
            }
        }
    }
}

@end
