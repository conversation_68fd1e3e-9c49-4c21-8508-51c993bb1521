//
//  IDFAGradientButton.h
//  GETIDFA
//
//  Created by apple on 2025/6/13.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, IDFAButtonSize) {
    IDFAButtonSizeNormal = 0, // 正常大小 (44pt高度)
    IDFAButtonSizeSmall = 1,  // 小按钮 (36pt高度)
    IDFAButtonSizeMini = 2    // 迷你按钮 (28pt高度)
};

@interface IDFAGradientButton : UIButton

- (instancetype)initWithTitle:(NSString *)title;
- (instancetype)initWithTitle:(NSString *)title size:(IDFAButtonSize)size;
- (void)updateThemeAppearance;

// 便利方法
+ (CGFloat)recommendedHeightForSize:(IDFAButtonSize)size;
- (CGFloat)recommendedHeight;

@end

NS_ASSUME_NONNULL_END
