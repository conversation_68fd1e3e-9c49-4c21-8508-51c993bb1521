//
//  IDFAGradientView.m
//  GETIDFA
//
//  Created by apple on 2025/6/13.
//

#import "IDFAGradientView.h"
#import "IDFAThemeManager.h"

@interface IDFAGradientView ()
@property (nonatomic, strong) CAGradientLayer *gradientLayer;
@property (nonatomic, strong) UIVisualEffectView *blurEffectView;
@property (nonatomic, assign) BOOL hasStartedAnimations;
@end

@implementation IDFAGradientView

- (instancetype)initWithFrame:(CGRect)frame {
    self = [super initWithFrame:frame];
    if (self) {
        [self setupGradientBackground];
        [self addGlassEffect];
        // 不在init中启动动画，等待合适的时机
        
        // 监听主题切换通知
        [[NSNotificationCenter defaultCenter] addObserver:self
                                                 selector:@selector(themeDidChange:)
                                                     name:@"IDFAThemeDidChangeNotification"
                                                   object:nil];
    }
    return self;
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

- (void)themeDidChange:(NSNotification *)notification {
    [self updateThemeAppearance];
}

- (void)layoutSubviews {
    [super layoutSubviews];

    // 更新子视图frame
    self.gradientLayer.frame = self.bounds;
    self.blurEffectView.frame = self.bounds;
}

- (void)setupGradientBackground {
    // 创建现代蓝紫渐变背景
    self.gradientLayer = [CAGradientLayer layer];
    
    // 使用主题管理器的渐变色
    [self updateGradientColors];
    
    // 设置渐变位置和方向
    self.gradientLayer.locations = @[@0.0, @0.5, @1.0];
    self.gradientLayer.startPoint = CGPointMake(0.0, 0.0);
    self.gradientLayer.endPoint = CGPointMake(1.0, 1.0);
    
    [self.layer insertSublayer:self.gradientLayer atIndex:0];
}

- (void)updateGradientColors {
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];
    NSArray<UIColor *> *gradientColors = theme.gradientColors;
    
    NSMutableArray *cgColors = [NSMutableArray array];
    for (UIColor *color in gradientColors) {
        [cgColors addObject:(id)color.CGColor];
    }
    
    self.gradientLayer.colors = cgColors;
}

- (void)addGlassEffect {
    // 添加毛玻璃效果
    UIBlurEffect *blurEffect = [UIBlurEffect effectWithStyle:UIBlurEffectStyleSystemUltraThinMaterial];
    self.blurEffectView = [[UIVisualEffectView alloc] initWithEffect:blurEffect];
    self.blurEffectView.alpha = 0.3; // 轻微的毛玻璃效果
    [self addSubview:self.blurEffectView];
}

- (void)addEnhancedAnimations {
    
    // 0.添加柔和的灯光效果
    [self addSoftLightEffect];
    
    // 1. 渐变色动画
    [self addGradientColorAnimation];

    // 2. 多层柔光效果
    [self addMultiLayerLightEffect];

    // 3. 浮动粒子效果
    [self addFloatingParticles];

    // 4. 波纹效果
    [self addRippleEffect];
}

- (void)addSoftLightEffect {
    // 添加柔光折射效果
    CALayer *lightLayer = [CALayer layer];
    
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];
    
    // 根据主题调整光效颜色和强度
    if (theme.currentTheme == IDFAThemeTypeDark) {
        lightLayer.backgroundColor = [UIColor colorWithWhite:0.8 alpha:0.08].CGColor;
        lightLayer.shadowColor = [UIColor colorWithWhite:0.9 alpha:1.0].CGColor;
        lightLayer.shadowOpacity = 0.2; // 暗黑主题下光效稍微柔和
    } else {
        lightLayer.backgroundColor = [UIColor colorWithWhite:1.0 alpha:0.1].CGColor;
        lightLayer.shadowColor = [UIColor whiteColor].CGColor;
        lightLayer.shadowOpacity = 0.3; // 亮色主题保持原有强度
    }
    
    lightLayer.cornerRadius = 100;
    lightLayer.frame = CGRectMake(-50, -50, 200, 200);
    lightLayer.shadowOffset = CGSizeZero;
    lightLayer.shadowRadius = 50;
    
    [self.layer addSublayer:lightLayer];
    
    // 添加动画效果
    CABasicAnimation *moveAnimation = [CABasicAnimation animationWithKeyPath:@"position"];
    moveAnimation.duration = 8.0;
    moveAnimation.repeatCount = INFINITY;
    moveAnimation.autoreverses = YES;
    moveAnimation.fromValue = [NSValue valueWithCGPoint:CGPointMake(100, 100)];
    moveAnimation.toValue = [NSValue valueWithCGPoint:CGPointMake(300, 300)];
    
    [lightLayer addAnimation:moveAnimation forKey:@"softLightMove"];
}

- (void)addGradientColorAnimation {
    // 渐变背景颜色缓慢变化
    CABasicAnimation *colorAnimation = [CABasicAnimation animationWithKeyPath:@"colors"];
    colorAnimation.duration = 12.0;
    colorAnimation.repeatCount = INFINITY;
    colorAnimation.autoreverses = YES;

    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];
    NSArray<UIColor *> *gradientColors = theme.gradientColors;
    
    // 基础颜色组合（当前主题的渐变色）
    NSMutableArray *fromColors = [NSMutableArray array];
    for (UIColor *color in gradientColors) {
        [fromColors addObject:(id)color.CGColor];
    }
    
    // 变化后的颜色组合（稍微调整亮度和色相）
    NSMutableArray *toColors = [NSMutableArray array];
    for (UIColor *color in gradientColors) {
        // 轻微调整颜色亮度和饱和度
        CGFloat hue, saturation, brightness, alpha;
        [color getHue:&hue saturation:&saturation brightness:&brightness alpha:&alpha];
        
        // 微调色相和亮度
        hue = fmod(hue + 0.05, 1.0);
        brightness = MIN(1.0, brightness + 0.1);
        
        UIColor *adjustedColor = [UIColor colorWithHue:hue saturation:saturation brightness:brightness alpha:alpha];
        [toColors addObject:(id)adjustedColor.CGColor];
    }

    colorAnimation.fromValue = fromColors;
    colorAnimation.toValue = toColors;

    [self.gradientLayer addAnimation:colorAnimation forKey:@"gradientColorAnimation"];
}

- (void)addMultiLayerLightEffect {
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];
    
    // 创建3个不同大小的柔光层
    for (int i = 0; i < 3; i++) {
        CALayer *lightLayer = [CALayer layer];
        
        // 根据主题调整多层光效
        if (theme.currentTheme == IDFAThemeTypeDark) {
            lightLayer.backgroundColor = [UIColor colorWithWhite:0.7 alpha:0.04 + i * 0.015].CGColor;
            lightLayer.shadowColor = [UIColor colorWithWhite:0.85 alpha:1.0].CGColor;
            lightLayer.shadowOpacity = 0.15 + (i * 0.04);
        } else {
            lightLayer.backgroundColor = [UIColor colorWithWhite:1.0 alpha:0.06 + i * 0.02].CGColor;
            lightLayer.shadowColor = [UIColor whiteColor].CGColor;
            lightLayer.shadowOpacity = 0.2 + (i * 0.05);
        }
        
        lightLayer.cornerRadius = 60 + (i * 30);
        lightLayer.shadowOffset = CGSizeZero;
        lightLayer.shadowRadius = 30 + (i * 15);

        [self.layer addSublayer:lightLayer];

        // 移动动画
        CABasicAnimation *moveAnimation = [CABasicAnimation animationWithKeyPath:@"position"];
        moveAnimation.duration = 8.0 + (i * 3.0);
        moveAnimation.repeatCount = INFINITY;
        moveAnimation.autoreverses = YES;
        moveAnimation.fromValue = [NSValue valueWithCGPoint:CGPointMake(50 + i * 80, 100 + i * 120)];
        moveAnimation.toValue = [NSValue valueWithCGPoint:CGPointMake(300 + i * 50, 500 + i * 80)];

        // 缩放动画
        CABasicAnimation *scaleAnimation = [CABasicAnimation animationWithKeyPath:@"transform.scale"];
        scaleAnimation.duration = 6.0 + (i * 2.0);
        scaleAnimation.repeatCount = INFINITY;
        scaleAnimation.autoreverses = YES;
        scaleAnimation.fromValue = @(0.7 + i * 0.1);
        scaleAnimation.toValue = @(1.3 + i * 0.1);

        // 组合动画
        CAAnimationGroup *group = [CAAnimationGroup animation];
        group.animations = @[moveAnimation, scaleAnimation];
        group.duration = moveAnimation.duration;
        group.repeatCount = INFINITY;

        [lightLayer addAnimation:group forKey:[NSString stringWithFormat:@"lightEffect%d", i]];
    }
}

- (void)addFloatingParticles {
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];
    
    // 创建12个浮动粒子
    for (int i = 0; i < 12; i++) {
        CALayer *particle = [CALayer layer];
        
        // 根据主题调整粒子颜色
        if (theme.currentTheme == IDFAThemeTypeDark) {
            particle.backgroundColor = [UIColor colorWithWhite:0.8 alpha:0.3].CGColor;
        } else {
            particle.backgroundColor = [UIColor colorWithWhite:1.0 alpha:0.4].CGColor;
        }

        // 不同大小的粒子
        CGFloat size = 2 + (i % 4);
        particle.cornerRadius = size / 2;
        particle.frame = CGRectMake(0, 0, size, size);

        // 随机初始位置
        particle.position = CGPointMake(arc4random_uniform(350), arc4random_uniform(700));

        [self.layer addSublayer:particle];

        // 垂直浮动动画
        CABasicAnimation *floatAnimation = [CABasicAnimation animationWithKeyPath:@"position.y"];
        floatAnimation.duration = 4.0 + (i * 0.5);
        floatAnimation.repeatCount = INFINITY;
        floatAnimation.autoreverses = YES;
        floatAnimation.fromValue = @(particle.position.y);
        floatAnimation.toValue = @(particle.position.y - 80 - (i * 5));

        // 水平漂移动画
        CABasicAnimation *driftAnimation = [CABasicAnimation animationWithKeyPath:@"position.x"];
        driftAnimation.duration = 6.0 + (i * 0.8);
        driftAnimation.repeatCount = INFINITY;
        driftAnimation.autoreverses = YES;
        driftAnimation.fromValue = @(particle.position.x);
        driftAnimation.toValue = @(particle.position.x + 30 - (i % 3) * 20);

        // 透明度动画
        CABasicAnimation *opacityAnimation = [CABasicAnimation animationWithKeyPath:@"opacity"];
        opacityAnimation.duration = 3.0 + (i * 0.4);
        opacityAnimation.repeatCount = INFINITY;
        opacityAnimation.autoreverses = YES;
        opacityAnimation.fromValue = @(0.1);
        opacityAnimation.toValue = @(0.8);

        // 组合所有动画
        CAAnimationGroup *particleGroup = [CAAnimationGroup animation];
        particleGroup.animations = @[floatAnimation, driftAnimation, opacityAnimation];
        particleGroup.duration = floatAnimation.duration;
        particleGroup.repeatCount = INFINITY;

        [particle addAnimation:particleGroup forKey:[NSString stringWithFormat:@"particle%d", i]];
    }
}

- (void)addRippleEffect {
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];
    
    // 创建波纹效果
    for (int i = 0; i < 2; i++) {
        CALayer *ripple = [CALayer layer];
        
        // 根据主题调整波纹颜色
        if (theme.currentTheme == IDFAThemeTypeDark) {
            ripple.borderColor = [UIColor colorWithWhite:0.7 alpha:0.25].CGColor;
        } else {
            ripple.borderColor = [UIColor colorWithWhite:1.0 alpha:0.3].CGColor;
        }
        
        ripple.borderWidth = 1.0;
        ripple.backgroundColor = [UIColor clearColor].CGColor;
        ripple.cornerRadius = 50;
        ripple.frame = CGRectMake(0, 0, 100, 100);
        ripple.position = CGPointMake(200 + i * 100, 400 + i * 150);

        [self.layer addSublayer:ripple];

        // 扩散动画
        CABasicAnimation *scaleAnimation = [CABasicAnimation animationWithKeyPath:@"transform.scale"];
        scaleAnimation.duration = 4.0 + i * 2.0;
        scaleAnimation.repeatCount = INFINITY;
        scaleAnimation.fromValue = @(0.5);
        scaleAnimation.toValue = @(2.0);

        // 透明度动画
        CABasicAnimation *opacityAnimation = [CABasicAnimation animationWithKeyPath:@"opacity"];
        opacityAnimation.duration = scaleAnimation.duration;
        opacityAnimation.repeatCount = INFINITY;
        opacityAnimation.fromValue = @(0.8);
        opacityAnimation.toValue = @(0.0);

        // 组合动画
        CAAnimationGroup *rippleGroup = [CAAnimationGroup animation];
        rippleGroup.animations = @[scaleAnimation, opacityAnimation];
        rippleGroup.duration = scaleAnimation.duration;
        rippleGroup.repeatCount = INFINITY;

        [ripple addAnimation:rippleGroup forKey:[NSString stringWithFormat:@"ripple%d", i]];
    }
}

- (void)restartAnimations {
    // 完全清除所有动画和动画层
    [self cleanupAllAnimations];

    // 等待一个runloop周期确保清理完成
    dispatch_async(dispatch_get_main_queue(), ^{
        // 重新启动动画
        [self addEnhancedAnimations];
    });
}

- (void)cleanupAllAnimations {
    // 1. 移除主层的所有动画
    [self.layer removeAllAnimations];

    // 2. 递归移除所有子层及其动画
    [self recursivelyRemoveAnimationsFromLayer:self.layer];

    // 3. 移除除了gradientLayer和blurEffectView之外的所有动画子层
    NSMutableArray *layersToRemove = [NSMutableArray array];
    for (CALayer *sublayer in self.layer.sublayers) {
        if (sublayer != self.gradientLayer && sublayer != self.blurEffectView.layer) {
            [layersToRemove addObject:sublayer];
        }
    }

    for (CALayer *layer in layersToRemove) {
        [layer removeFromSuperlayer];
    }

    // 4. 重置gradientLayer的动画
    if (self.gradientLayer) {
        [self.gradientLayer removeAllAnimations];
    }

    // 5. 重置动画状态，允许重新启动
    self.hasStartedAnimations = NO;

//    NSLog(@"动画清理完成，剩余子层数量: %lu", (unsigned long)self.layer.sublayers.count);
}

- (void)recursivelyRemoveAnimationsFromLayer:(CALayer *)layer {
    // 移除当前层的所有动画
    [layer removeAllAnimations];

    // 递归处理所有子层
    for (CALayer *sublayer in layer.sublayers) {
        [self recursivelyRemoveAnimationsFromLayer:sublayer];
    }
}

- (void)updateThemeAppearance {
    // 更新渐变背景色
    [self updateGradientColors];
    
    // 更新毛玻璃效果
    [self updateGlassEffectForTheme];
    
    // 如果已经启动了动画，重新启动以适配新主题
    if (self.hasStartedAnimations) {
        [self restartAnimations];
    }
}

- (void)updateGlassEffectForTheme {
    IDFAThemeManager *theme = [IDFAThemeManager sharedManager];
    
    if (self.blurEffectView) {
        // 根据主题调整毛玻璃效果
        UIBlurEffectStyle blurStyle;
        CGFloat alpha;
        
        if (theme.currentTheme == IDFAThemeTypeDark) {
            blurStyle = UIBlurEffectStyleSystemUltraThinMaterialDark;
            alpha = 0.4; // 暗黑主题下稍微增强毛玻璃效果
        } else {
            blurStyle = UIBlurEffectStyleSystemUltraThinMaterial;
            alpha = 0.3; // 亮色主题保持原有效果
        }
        
        UIBlurEffect *blurEffect = [UIBlurEffect effectWithStyle:blurStyle];
        self.blurEffectView.effect = blurEffect;
        self.blurEffectView.alpha = alpha;
    }
}

@end
